{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "validate": "tsc --noEmit && yarn lint", "test": "jest --runInBand", "test:watch": "jest --watch", "test:coverage": "jest --coverage --runInBand", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^10.5.0", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.56.1", "@tanstack/react-query": "^5.87.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.462.0", "next": "15", "next-themes": "^0.3.0", "pino": "^9.9.0", "react": "18", "react-day-picker": "^8.10.1", "react-dom": "18", "react-hook-form": "^7.61.1", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.32.0", "@next/eslint-plugin-next": "^15.5.0", "@tailwindcss/typography": "^0.5.16", "@types/jest": "^30.0.0", "@types/node": "^22.16.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "husky": "^9.1.7", "jest": "^30.1.2", "lint-staged": "^16.1.5", "pino-pretty": "^13.1.1", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0", "whatwg-url": "^14.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,md,json,mdx}": ["prettier --write"]}}