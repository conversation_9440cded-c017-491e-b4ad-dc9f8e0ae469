/** @format */

import type { Db } from '@/data/types';
import { RepositoryError } from '@/lib/errors';

export interface CreateTaskInput {
  title: string;
  goal_id?: string;
  planned_for_date?: string; // ISO date
}

export async function createTask(
  db: Db,
  userId: string,
  input: CreateTaskInput,
) {
  const { error } = await db.from('tasks').insert({
    user_id: userId,
    title: input.title,
    goal_id: input.goal_id || null,
    planned_for_date: input.planned_for_date || null,
  });
  if (error) {
    throw new RepositoryError('Failed to create task', error, {
      userId,
      input,
    });
  }
}

export async function listTasksForDate(db: Db, userId: string, date?: string) {
  const query = db
    .from('tasks')
    .select('*')
    .eq('user_id', userId)
    .order('order', { ascending: true });

  if (date) query.eq('planned_for_date', date);

  const { data, error } = await query;
  if (error) {
    throw new RepositoryError('Failed to list tasks', error, { userId, date });
  }
  return data;
}
