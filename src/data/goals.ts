// CRUD operations for goals

import { Db } from '@/data/types';
import { RepositoryError } from '@/lib/errors';

export interface CreateGoalInput {
  id?: string;
  title: string;
  timeframe?: string;
  exclude_from_ai?: boolean;
}
export interface UpdateGoalInput {
  title?: string;
  timeframe?: string;
  exclude_from_ai?: boolean;
}

export async function createGoal(
  db: Db,
  userId: string,
  goal: CreateGoalInput,
) {
  const { error } = await db.from('goals').insert({ ...goal, user_id: userId });
  if (error) {
    throw new RepositoryError('Failed to create goal', error, { userId, goal });
  }
  return goal;
}

export async function listGoals(db: Db, userId: string) {
  const { data, error } = await db
    .from('goals')
    .select('*')
    .eq('user_id', userId);
  if (error) {
    throw new RepositoryError('Failed to list goals', error, { userId });
  }
  return data;
}

export async function getGoal(db: Db, userId: string, goalId: string) {
  const { data, error } = await db
    .from('goals')
    .select('*')
    .eq('user_id', userId)
    .eq('id', goalId);
  if (error) {
    throw new RepositoryError('Failed to get goal', error, { userId, goalId });
  }
  return data;
}

export async function updateGoal(
  db: Db,
  userId: string,
  goalId: string,
  goal: UpdateGoalInput,
) {
  const { error } = await db
    .from('goals')
    .update(goal)
    .eq('user_id', userId)
    .eq('id', goalId);
  if (error) {
    throw new RepositoryError('Failed to update goal', error, {
      userId,
      goalId,
      goal,
    });
  }
}
