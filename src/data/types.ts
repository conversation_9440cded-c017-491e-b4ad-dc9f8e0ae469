/** @format */

import type { SupabaseClient } from '@supabase/supabase-js';
import type {
  Database,
  Tables,
  TablesInsert,
  TablesUpdate,
  Enums,
} from '@/types/database.types';

export type Task = Database['public']['Tables']['tasks']['Row'];
export type Goal = Database['public']['Tables']['goals']['Row'];
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type LogEntry = Database['public']['Tables']['log_entries']['Row'];

export type TaskStatus = Database['public']['Enums']['task_status'];
export type LogKind = Database['public']['Enums']['log_kind'];

export type Db = SupabaseClient<Database>;

export type { Tables, TablesInsert, TablesUpdate, Enums };
