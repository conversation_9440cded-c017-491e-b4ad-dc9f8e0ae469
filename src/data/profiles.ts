/** @format */

import type { Db } from '@/data/types';
import { RepositoryError } from '@/lib/errors';

export async function upsertProfile(db: Db, userId: string): Promise<void> {
  const { error } = await db
    .from('profiles')
    .upsert({ user_id: userId }, { onConflict: 'user_id' });
  if (error) {
    throw new RepositoryError('Failed to upsert profile', error, { userId });
  }
}

export async function upsertSettings(db: Db, userId: string): Promise<void> {
  const { error } = await db
    .from('settings')
    .upsert({ user_id: userId }, { onConflict: 'user_id' });
  if (error) {
    throw new RepositoryError('Failed to upsert settings', error, { userId });
  }
}
