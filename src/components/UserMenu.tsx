/** @format */

'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Bell, HelpCircle, LogOut, Settings, User } from 'lucide-react';

type UserInfo = {
  name?: string | null;
  email?: string | null;
  avatarUrl?: string | null;
};

export function UserMenu() {
  const [userInfo, setUserInfo] = useState<UserInfo>({});

  useEffect(() => {
    const supabase = createClient();
    supabase.auth.getUser().then(({ data }) => {
      const user = data.user;
      if (user) {
        setUserInfo({
          name: user.user_metadata?.full_name ?? null,
          email: user.email ?? null,
          avatarUrl: user.user_metadata?.avatar_url ?? null,
        });
      }
    });
  }, []);

  const initials = (userInfo.name || userInfo.email || 'U')
    .slice(0, 2)
    .toUpperCase();

  const handleSignOut = async () => {
    await fetch('/auth/signout', { method: 'POST' });
    window.location.href = '/login';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          aria-label='Open account menu'
          className='inline-flex items-center justify-center rounded-full outline-none ring-offset-background transition focus-visible:ring-2 focus-visible:ring-ring'
        >
          <Avatar className='h-9 w-9'>
            {userInfo.avatarUrl ? (
              <AvatarImage
                src={userInfo.avatarUrl}
                alt={userInfo.name || 'User'}
              />
            ) : null}
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end' className='w-64'>
        <DropdownMenuLabel>
          <div className='flex items-center gap-3'>
            <Avatar className='h-8 w-8'>
              {userInfo.avatarUrl ? (
                <AvatarImage
                  src={userInfo.avatarUrl}
                  alt={userInfo.name || 'User'}
                />
              ) : null}
              <AvatarFallback className='text-xs'>{initials}</AvatarFallback>
            </Avatar>
            <div className='space-y-0.5'>
              <div className='text-sm font-medium leading-none'>
                {userInfo.name || 'Welcome'}
              </div>
              <div className='text-xs text-muted-foreground truncate'>
                {userInfo.email}
              </div>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => (window.location.href = '/profile')}>
          <User className='mr-2 h-4 w-4' /> View Profile
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => (window.location.href = '/settings')}>
          <Settings className='mr-2 h-4 w-4' /> Preferences
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => (window.location.href = '/notifications')}
        >
          <Bell className='mr-2 h-4 w-4' /> Notifications
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => (window.location.href = '/help')}>
          <HelpCircle className='mr-2 h-4 w-4' /> Help & Support
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleSignOut}
          className='text-red-600 focus:text-red-600'
        >
          <LogOut className='mr-2 h-4 w-4' /> Sign Out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
