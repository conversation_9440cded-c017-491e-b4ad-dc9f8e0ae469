'use client';

import { useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useSentryInteractionTracking } from '@/hooks/use-sentry-performance';
import { measureAsync, setUserContext, captureMessage } from '@/lib/sentry';
import { AlertTriangle, Zap, User, Activity } from 'lucide-react';

export function SentryTestComponent() {
  const [lastAction, setLastAction] = useState<string>('');
  const { trackClick, trackCustomAction } = useSentryInteractionTracking();

  const testClientError = () => {
    trackClick('test-client-error');
    throw new Error(
      'This is a test client-side error from SentryTestComponent',
    );
  };

  const testPromiseRejection = () => {
    trackClick('test-promise-rejection');
    Promise.reject(new Error('This is a test unhandled promise rejection'));
    setLastAction('Triggered promise rejection');
  };

  const testPerformanceTracking = async () => {
    trackClick('test-performance');

    const result = await measureAsync(
      'test-operation',
      async () => {
        // Simulate some async work
        return new Promise((resolve) =>
          setTimeout(() => resolve('Operation completed'), 1000),
        );
      },
      { testType: 'performance-demo' },
    );

    setLastAction(`Performance test completed: ${result}`);
  };

  const testUserContext = () => {
    trackClick('test-user-context');
    setUserContext({
      id: 'test-user-123',
      email: '<EMAIL>',
      username: 'testuser',
      testSession: true,
    });
    setLastAction('User context set');
  };

  const testCustomEvent = () => {
    trackClick('test-custom-event');
    captureMessage('Custom test event triggered', 'info', {
      component: 'SentryTestComponent',
      timestamp: new Date().toISOString(),
    });
    setLastAction('Custom event captured');
  };

  const testManualException = () => {
    trackClick('test-manual-exception');
    Sentry.captureException(new Error('Manually captured test exception'), {
      tags: { testType: 'manual' },
      extra: { source: 'SentryTestComponent' },
    });
    setLastAction('Manual exception captured');
  };

  return (
    <Card className='w-full max-w-2xl'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <AlertTriangle className='h-5 w-5' />
          Sentry Testing Dashboard
        </CardTitle>
        <CardDescription>
          Use these buttons to test Sentry error monitoring and performance
          tracking.
          {process.env.NODE_ENV === 'development' &&
            !process.env.SENTRY_ENABLE_DEV && (
              <span className='block mt-2 text-yellow-600'>
                ⚠️ Sentry is disabled in development. Set SENTRY_ENABLE_DEV=true
                to test.
              </span>
            )}
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        {lastAction && (
          <div className='p-3 bg-muted rounded-md'>
            <p className='text-sm text-muted-foreground'>
              Last action: {lastAction}
            </p>
          </div>
        )}

        <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
          <Button
            onClick={testClientError}
            variant='destructive'
            className='flex items-center gap-2'
          >
            <AlertTriangle className='h-4 w-4' />
            Test Client Error
          </Button>

          <Button
            onClick={testPromiseRejection}
            variant='destructive'
            className='flex items-center gap-2'
          >
            <AlertTriangle className='h-4 w-4' />
            Test Promise Rejection
          </Button>

          <Button
            onClick={testPerformanceTracking}
            variant='outline'
            className='flex items-center gap-2'
          >
            <Activity className='h-4 w-4' />
            Test Performance
          </Button>

          <Button
            onClick={testUserContext}
            variant='outline'
            className='flex items-center gap-2'
          >
            <User className='h-4 w-4' />
            Set User Context
          </Button>

          <Button
            onClick={testCustomEvent}
            variant='outline'
            className='flex items-center gap-2'
          >
            <Zap className='h-4 w-4' />
            Capture Custom Event
          </Button>

          <Button
            onClick={testManualException}
            variant='outline'
            className='flex items-center gap-2'
          >
            <AlertTriangle className='h-4 w-4' />
            Manual Exception
          </Button>
        </div>

        <div className='text-xs text-muted-foreground space-y-1'>
          <p>• Check your browser's Network tab to see requests to Sentry</p>
          <p>• Check your Sentry dashboard to see captured events</p>
          <p>• Error buttons will trigger the error boundary</p>
        </div>
      </CardContent>
    </Card>
  );
}
