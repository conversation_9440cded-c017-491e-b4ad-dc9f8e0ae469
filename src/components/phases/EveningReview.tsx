/** @format */

import { useState } from 'react';
import {
  <PERSON>,
  ArrowLef<PERSON>,
  TrendingUp,
  Target,
  Calendar,
  Star,
} from 'lucide-react';
import { Task, LogEntry } from '@/components/DailyCompass';

interface EveningReviewProps {
  tasks: Task[];
  logEntries: LogEntry[];
  onAddReflection: (content: string) => void;
  onBackToActive: () => void;
}

export const EveningReview = ({
  tasks,
  logEntries,
  onAddReflection,
  onBackToActive,
}: EveningReviewProps) => {
  const [reflection, setReflection] = useState('');
  const [tomorrowIdeas, setTomorrowIdeas] = useState('');

  const completedTasks = tasks.filter((t) => t.completed);
  const totalTasks = tasks.length;
  const completionRate =
    totalTasks > 0 ? Math.round((completedTasks.length / totalTasks) * 100) : 0;

  const focusSessions = logEntries.filter(
    (entry) => entry.type === 'focus',
  ).length;
  const reflectionEntries = logEntries.filter(
    (entry) => entry.type === 'reflection',
  );

  const addReflectionNote = () => {
    if (reflection.trim()) {
      onAddReflection(reflection.trim());
      setReflection('');
    }
  };

  const generateSummary = () => {
    return `Today you completed ${
      completedTasks.length
    } out of ${totalTasks} tasks and logged ${focusSessions} focus sessions. ${
      completionRate >= 80
        ? 'Excellent progress on your goals!'
        : completionRate >= 60
          ? 'Good steady progress today.'
          : 'A slower day, but every step counts.'
    }`;
  };

  return (
    <div className='min-h-screen bg-gradient-subtle p-6'>
      <div className='max-w-4xl mx-auto'>
        <div className='flex items-center gap-4 mb-8'>
          <button
            onClick={onBackToActive}
            className='p-2 text-muted-foreground hover:text-foreground transition-smooth'
          >
            <ArrowLeft className='h-5 w-5' />
          </button>
          <div className='flex items-center gap-3'>
            <div className='p-2 bg-gradient-primary rounded-lg'>
              <Moon className='h-6 w-6 text-primary-foreground' />
            </div>
            <div>
              <h1 className='text-2xl font-bold text-foreground'>
                Evening Review
              </h1>
              <p className='text-muted-foreground'>
                Reflect on your progress and plan for tomorrow
              </p>
            </div>
          </div>
        </div>

        {/* AI Summary */}
        <div className='bg-card rounded-xl shadow-elegant border border-border p-6 mb-6'>
          <div className='flex items-start gap-3 mb-4'>
            <TrendingUp className='h-5 w-5 text-success mt-0.5' />
            <div>
              <h3 className='font-medium text-foreground mb-2'>
                Daily Summary
              </h3>
              <p className='text-muted-foreground leading-relaxed'>
                {generateSummary()}
              </p>
            </div>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mt-6'>
            <div className='text-center p-4 bg-success/5 border border-success/20 rounded-lg'>
              <div className='text-2xl font-bold text-success'>
                {completedTasks.length}
              </div>
              <div className='text-sm text-muted-foreground'>
                Tasks Completed
              </div>
            </div>
            <div className='text-center p-4 bg-focus/5 border border-focus/20 rounded-lg'>
              <div className='text-2xl font-bold text-focus'>
                {focusSessions}
              </div>
              <div className='text-sm text-muted-foreground'>
                Focus Sessions
              </div>
            </div>
            <div className='text-center p-4 bg-primary/5 border border-primary/20 rounded-lg'>
              <div className='text-2xl font-bold text-primary'>
                {completionRate}%
              </div>
              <div className='text-sm text-muted-foreground'>
                Completion Rate
              </div>
            </div>
          </div>
        </div>

        {/* Task Breakdown */}
        <div className='bg-card rounded-xl shadow-elegant border border-border p-6 mb-6'>
          <div className='flex items-start gap-3 mb-4'>
            <Target className='h-5 w-5 text-primary mt-0.5' />
            <h3 className='font-medium text-foreground'>Task Breakdown</h3>
          </div>

          <div className='space-y-3'>
            {tasks.map((task) => (
              <div
                key={task.id}
                className={`p-3 rounded-lg border ${
                  task.completed
                    ? 'border-success/20 bg-success/5 text-success'
                    : 'border-muted bg-muted/20 text-muted-foreground'
                }`}
              >
                <div className='flex items-center gap-3'>
                  {task.completed ? (
                    <Star className='h-4 w-4 text-success' />
                  ) : (
                    <div className='w-4 h-4 border-2 border-muted-foreground rounded' />
                  )}
                  <span
                    className={`text-sm ${task.completed ? 'font-medium' : ''}`}
                  >
                    {task.title}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Reflection */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-6'>
          <div className='bg-card rounded-xl shadow-elegant border border-border p-6'>
            <h3 className='font-medium text-foreground mb-4'>
              What went well today?
            </h3>
            <textarea
              value={reflection}
              onChange={(e) => setReflection(e.target.value)}
              placeholder='Reflect on your successes, breakthroughs, or moments of flow...'
              className='w-full h-32 bg-input border border-border rounded-lg p-3 text-foreground placeholder:text-muted-foreground resize-none outline-none focus:border-primary transition-smooth'
            />
            <button
              onClick={addReflectionNote}
              disabled={!reflection.trim()}
              className='mt-3 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-smooth'
            >
              Add Reflection
            </button>
          </div>

          <div className='bg-card rounded-xl shadow-elegant border border-border p-6'>
            <h3 className='font-medium text-foreground mb-4'>
              Ideas for tomorrow
            </h3>
            <textarea
              value={tomorrowIdeas}
              onChange={(e) => setTomorrowIdeas(e.target.value)}
              placeholder='What would you like to focus on tomorrow? Any priorities or ideas to capture...'
              className='w-full h-32 bg-input border border-border rounded-lg p-3 text-foreground placeholder:text-muted-foreground resize-none outline-none focus:border-primary transition-smooth'
            />
            <button
              onClick={() => {
                if (tomorrowIdeas.trim()) {
                  onAddReflection(
                    `Ideas for tomorrow: ${tomorrowIdeas.trim()}`,
                  );
                  setTomorrowIdeas('');
                }
              }}
              disabled={!tomorrowIdeas.trim()}
              className='mt-3 px-4 py-2 bg-focus text-focus-foreground rounded-lg hover:bg-focus/90 disabled:opacity-50 disabled:cursor-not-allowed transition-smooth'
            >
              Save Ideas
            </button>
          </div>
        </div>

        {/* Previous Reflections */}
        {reflectionEntries.length > 0 && (
          <div className='bg-card rounded-xl shadow-elegant border border-border p-6'>
            <div className='flex items-start gap-3 mb-4'>
              <Calendar className='h-5 w-5 text-accent-foreground mt-0.5' />
              <h3 className='font-medium text-foreground'>
                Recent Reflections
              </h3>
            </div>
            <div className='space-y-3'>
              {reflectionEntries.slice(0, 3).map((entry) => (
                <div
                  key={entry.id}
                  className='p-3 bg-accent/5 border border-accent/20 rounded-lg'
                >
                  <p className='text-sm text-foreground leading-relaxed'>
                    {entry.content}
                  </p>
                  <p className='text-xs text-muted-foreground mt-2'>
                    {entry.timestamp.toLocaleDateString()} at{' '}
                    {entry.timestamp.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className='text-center mt-8'>
          <button
            onClick={onBackToActive}
            className='inline-flex items-center gap-2 px-6 py-3 bg-gradient-primary text-primary-foreground rounded-xl font-medium shadow-elegant hover:shadow-glow transition-smooth'
          >
            <span>Return to Daily Compass</span>
          </button>
        </div>
      </div>
    </div>
  );
};
