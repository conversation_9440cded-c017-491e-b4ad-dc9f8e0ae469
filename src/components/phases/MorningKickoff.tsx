/** @format */

import { useState } from 'react';
import { Sun, ArrowRight, Lightbulb, Target } from 'lucide-react';
import { Goal, Task, LogEntry } from '@/components/DailyCompass';

interface MorningKickoffProps {
  previousNotes: LogEntry[];
  goals: Goal[];
  onStartDay: () => void;
  onAddTask: (task: Task) => void;
}

export const MorningKickoff = ({
  previousNotes,
  goals,
  onStartDay,
  onAddTask,
}: MorningKickoffProps) => {
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [customTask, setCustomTask] = useState('');

  const suggestedTasks = [
    'Review email and prioritize responses',
    'Draft campaign strategy outline',
    'Analyze Q3 financial data',
    'Schedule team check-in meetings',
    'Update project status documentation',
  ];

  const toggleTask = (task: string) => {
    setSelectedTasks((prev) =>
      prev.includes(task) ? prev.filter((t) => t !== task) : [...prev, task],
    );
  };

  const addCustomTask = () => {
    if (customTask.trim()) {
      setSelectedTasks((prev) => [...prev, customTask.trim()]);
      setCustomTask('');
    }
  };

  const startDayWithTasks = () => {
    selectedTasks.forEach((taskTitle, index) => {
      const task: Task = {
        id: Date.now().toString() + index,
        title: taskTitle,
        completed: false,
        estimatedTime: 25,
      };
      onAddTask(task);
    });
    onStartDay();
  };

  const lastNote = previousNotes[0];

  return (
    <div className='min-h-screen bg-gradient-subtle flex items-center justify-center p-6'>
      <div className='max-w-2xl w-full'>
        <div className='text-center mb-8'>
          <div className='inline-flex items-center justify-center w-16 h-16 bg-gradient-primary rounded-full mb-4'>
            <Sun className='h-8 w-8 text-primary-foreground' />
          </div>
          <h1 className='text-3xl font-bold text-foreground mb-2'>
            Good Morning!
          </h1>
          <p className='text-muted-foreground'>
            Let's set your intentions for a productive day
          </p>
        </div>

        <div className='bg-card rounded-xl shadow-elegant border border-border p-6 mb-6'>
          <div className='flex items-start gap-3 mb-4'>
            <Lightbulb className='h-5 w-5 text-accent-foreground mt-0.5' />
            <div>
              <h3 className='font-medium text-foreground mb-2'>
                Yesterday's Reflection
              </h3>
              {lastNote ? (
                <p className='text-sm text-muted-foreground leading-relaxed'>
                  "{lastNote.content}"
                </p>
              ) : (
                <p className='text-sm text-muted-foreground italic'>
                  No notes from yesterday. Today's a fresh start!
                </p>
              )}
            </div>
          </div>
        </div>

        <div className='bg-card rounded-xl shadow-elegant border border-border p-6 mb-6'>
          <div className='flex items-start gap-3 mb-4'>
            <Target className='h-5 w-5 text-primary mt-0.5' />
            <div>
              <h3 className='font-medium text-foreground mb-2'>
                Your North Star Goals
              </h3>
              <div className='space-y-2'>
                {goals.map((goal) => (
                  <div
                    key={goal.id}
                    className='p-3 bg-primary/5 border border-primary/20 rounded-lg'
                  >
                    <p className='text-sm font-medium text-primary'>
                      {goal.title}
                    </p>
                    {goal.description && (
                      <p className='text-xs text-muted-foreground mt-1'>
                        {goal.description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className='bg-card rounded-xl shadow-elegant border border-border p-6 mb-6'>
          <h3 className='font-medium text-foreground mb-4'>
            What are the 1-3 most important things to accomplish today?
          </h3>

          <div className='space-y-3 mb-4'>
            {suggestedTasks.map((task) => (
              <button
                key={task}
                onClick={() => toggleTask(task)}
                className={`w-full p-3 text-left rounded-lg border-2 transition-smooth ${
                  selectedTasks.includes(task)
                    ? 'border-focus bg-focus/5 text-focus-foreground'
                    : 'border-border bg-background hover:border-primary/30'
                }`}
              >
                <div className='flex items-center gap-3'>
                  <div
                    className={`w-4 h-4 rounded border-2 transition-smooth ${
                      selectedTasks.includes(task)
                        ? 'bg-focus border-focus'
                        : 'border-muted-foreground'
                    }`}
                  />
                  <span className='text-sm'>{task}</span>
                </div>
              </button>
            ))}
          </div>

          <div className='flex gap-2'>
            <input
              type='text'
              value={customTask}
              onChange={(e) => setCustomTask(e.target.value)}
              placeholder='Add a custom task...'
              className='flex-1 px-3 py-2 bg-input border border-border rounded-lg text-foreground placeholder:text-muted-foreground outline-none focus:border-primary transition-smooth'
              onKeyDown={(e) => {
                if (e.key === 'Enter') addCustomTask();
              }}
            />
            <button
              onClick={addCustomTask}
              disabled={!customTask.trim()}
              className='px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-smooth'
            >
              Add
            </button>
          </div>
        </div>

        <div className='text-center'>
          <button
            onClick={startDayWithTasks}
            disabled={selectedTasks.length === 0}
            className='inline-flex items-center gap-2 px-8 py-4 bg-gradient-primary text-primary-foreground rounded-xl font-medium shadow-elegant hover:shadow-glow disabled:opacity-50 disabled:cursor-not-allowed transition-smooth'
          >
            <span>Start Your Day</span>
            <ArrowRight className='h-5 w-5' />
          </button>
          {selectedTasks.length === 0 && (
            <p className='text-sm text-muted-foreground mt-2'>
              Select at least one task to begin
            </p>
          )}
        </div>
      </div>
    </div>
  );
};
