/** @format */

import { useState } from 'react';
import {
  BookOpen,
  Plus,
  MessageCircle,
  CheckCircle,
  Focus,
  Lightbulb,
} from 'lucide-react';
import { LogEntry } from '@/components/DailyCompass';

interface TheLogProps {
  logEntries: LogEntry[];
  onAddEntry: (content: string, type: LogEntry['type']) => void;
}

export const TheLog = ({ logEntries, onAddEntry }: TheLogProps) => {
  const [newNote, setNewNote] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  const addNote = () => {
    if (newNote.trim()) {
      onAddEntry(newNote.trim(), 'note');
      setNewNote('');
      setShowAddForm(false);
    }
  };

  const getEntryIcon = (type: LogEntry['type']) => {
    switch (type) {
      case 'completion':
        return <CheckCircle className='h-4 w-4 text-success' />;
      case 'focus':
        return <Focus className='h-4 w-4 text-focus' />;
      case 'reflection':
        return <Lightbulb className='h-4 w-4 text-accent-foreground' />;
      default:
        return <MessageCircle className='h-4 w-4 text-muted-foreground' />;
    }
  };

  const getEntryColor = (type: LogEntry['type']) => {
    switch (type) {
      case 'completion':
        return 'border-l-success bg-success/5';
      case 'focus':
        return 'border-l-focus bg-focus/5';
      case 'reflection':
        return 'border-l-accent bg-accent/5';
      default:
        return 'border-l-border bg-background';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Group entries by date
  const groupedEntries = logEntries.reduce(
    (groups, entry) => {
      const dateKey = entry.timestamp.toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(entry);
      return groups;
    },
    {} as Record<string, LogEntry[]>,
  );

  return (
    <div className='bg-card rounded-xl shadow-elegant border border-border p-6 flex flex-col'>
      <div className='flex items-center justify-between mb-6'>
        <div className='flex items-center gap-3'>
          <div className='p-2 bg-accent rounded-lg'>
            <BookOpen className='h-5 w-5 text-accent-foreground' />
          </div>
          <h2 className='text-xl font-semibold text-card-foreground'>
            The Log
          </h2>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className='p-2 text-muted-foreground hover:text-accent-foreground transition-smooth'
        >
          <Plus className='h-4 w-4' />
        </button>
      </div>

      {showAddForm && (
        <div className='mb-4 p-4 border-2 border-dashed border-accent/30 rounded-lg bg-accent/5'>
          <textarea
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder='Add a note to your log...'
            className='w-full bg-transparent border-none outline-none text-foreground placeholder:text-muted-foreground resize-none'
            rows={3}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && e.ctrlKey) addNote();
              if (e.key === 'Escape') {
                setShowAddForm(false);
                setNewNote('');
              }
            }}
            autoFocus
          />
          <div className='flex gap-2 mt-3'>
            <button
              onClick={addNote}
              className='px-3 py-1 bg-accent text-accent-foreground text-sm rounded-md hover:bg-accent/80 transition-smooth'
            >
              Add Note
            </button>
            <button
              onClick={() => {
                setShowAddForm(false);
                setNewNote('');
              }}
              className='px-3 py-1 text-muted-foreground text-sm hover:text-foreground transition-smooth'
            >
              Cancel
            </button>
          </div>
          <p className='text-xs text-muted-foreground mt-2'>
            Press Ctrl+Enter to save, Escape to cancel
          </p>
        </div>
      )}

      <div className='flex-1 overflow-y-auto space-y-4'>
        {Object.entries(groupedEntries)
          .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
          .map(([dateString, entries]) => (
            <div key={dateString}>
              <div className='sticky top-0 bg-muted/50 backdrop-blur-sm px-3 py-1 rounded-md mb-3'>
                <h3 className='text-sm font-medium text-muted-foreground'>
                  {formatDate(new Date(dateString))}
                </h3>
              </div>
              <div className='space-y-3'>
                {entries.map((entry) => (
                  <div
                    key={entry.id}
                    className={`p-3 rounded-lg border-l-4 transition-smooth ${getEntryColor(
                      entry.type,
                    )}`}
                  >
                    <div className='flex items-start gap-3'>
                      {getEntryIcon(entry.type)}
                      <div className='flex-1'>
                        <p className='text-sm text-foreground leading-relaxed'>
                          {entry.content}
                        </p>
                        <p className='text-xs text-muted-foreground mt-2'>
                          {formatTime(entry.timestamp)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}

        {logEntries.length === 0 && (
          <div className='text-center py-8'>
            <BookOpen className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
            <p className='text-muted-foreground'>Your daily log is empty</p>
            <p className='text-sm text-muted-foreground mt-1'>
              Add notes, complete tasks, or start focus sessions to build your
              log
            </p>
          </div>
        )}
      </div>

      <div className='mt-6 p-3 bg-accent/20 rounded-lg'>
        <p className='text-sm text-accent-foreground'>
          📝 <strong>AI Tip:</strong> Your log automatically captures task
          completions and focus sessions.
        </p>
      </div>
    </div>
  );
};
