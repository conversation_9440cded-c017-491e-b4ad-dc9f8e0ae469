/** @format */

import { useState } from 'react';
import { Plus, CheckCircle2, Circle, Play, Clock } from 'lucide-react';
import { Task } from '@/components/DailyCompass';

interface TodaysPathProps {
  tasks: Task[];
  onCompleteTask: (taskId: string) => void;
  onStartFocus: (task: Task) => void;
  onUpdateTasks: (tasks: Task[]) => void;
}

export const TodaysPath = ({
  tasks,
  onCompleteTask,
  onStartFocus,
  onUpdateTasks,
}: TodaysPathProps) => {
  const [newTask, setNewTask] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  const addTask = () => {
    if (newTask.trim()) {
      const task: Task = {
        id: Date.now().toString(),
        title: newTask.trim(),
        completed: false,
        estimatedTime: 25,
      };
      onUpdateTasks([...tasks, task]);
      setNewTask('');
      setShowAddForm(false);
    }
  };

  const completedTasks = tasks.filter((t) => t.completed).length;
  const totalTasks = tasks.length;

  return (
    <div className='bg-card rounded-xl shadow-elegant border border-border p-6 flex flex-col'>
      <div className='flex items-center justify-between mb-6'>
        <div className='flex items-center gap-3'>
          <div className='p-2 bg-gradient-focus rounded-lg'>
            <Clock className='h-5 w-5 text-focus-foreground' />
          </div>
          <h2 className='text-xl font-semibold text-card-foreground'>
            Today's Path
          </h2>
        </div>
        {totalTasks > 0 && (
          <div className='text-sm text-muted-foreground'>
            {completedTasks}/{totalTasks} completed
          </div>
        )}
      </div>

      {/* Progress bar */}
      {totalTasks > 0 && (
        <div className='mb-6'>
          <div className='w-full bg-muted rounded-full h-2'>
            <div
              className='bg-gradient-primary h-2 rounded-full transition-smooth'
              style={{ width: `${(completedTasks / totalTasks) * 100}%` }}
            />
          </div>
        </div>
      )}

      <div className='flex-1 space-y-3'>
        {tasks.map((task) => (
          <div
            key={task.id}
            className={`group p-4 rounded-lg border transition-smooth ${
              task.completed
                ? 'border-success bg-success/5'
                : task.inProgress
                  ? 'border-focus bg-focus/5 animate-pulse-glow'
                  : 'border-border bg-background hover:border-primary/30 hover:shadow-md'
            }`}
          >
            <div className='flex items-start gap-3'>
              <button
                onClick={() => onCompleteTask(task.id)}
                className={`mt-0.5 transition-smooth ${
                  task.completed
                    ? 'text-success'
                    : 'text-muted-foreground hover:text-primary'
                }`}
              >
                {task.completed ? (
                  <CheckCircle2 className='h-5 w-5' />
                ) : (
                  <Circle className='h-5 w-5' />
                )}
              </button>

              <div className='flex-1'>
                <h3
                  className={`font-medium ${
                    task.completed
                      ? 'line-through text-muted-foreground'
                      : 'text-foreground'
                  }`}
                >
                  {task.title}
                </h3>
                {task.description && (
                  <p className='text-sm text-muted-foreground mt-1'>
                    {task.description}
                  </p>
                )}
                {task.estimatedTime && (
                  <div className='flex items-center gap-1 mt-2 text-xs text-muted-foreground'>
                    <Clock className='h-3 w-3' />
                    <span>{task.estimatedTime} min</span>
                  </div>
                )}
              </div>

              {!task.completed && !task.inProgress && (
                <button
                  onClick={() => onStartFocus(task)}
                  className='p-2 bg-focus text-focus-foreground rounded-lg hover:bg-focus/90 transition-smooth opacity-0 group-hover:opacity-100'
                  title='Start Focus Session'
                >
                  <Play className='h-4 w-4' />
                </button>
              )}

              {task.inProgress && (
                <div className='px-3 py-1 bg-focus text-focus-foreground text-xs rounded-full'>
                  In Progress
                </div>
              )}
            </div>
          </div>
        ))}

        {showAddForm ? (
          <div className='p-4 border-2 border-dashed border-focus/30 rounded-lg bg-focus/5'>
            <input
              type='text'
              value={newTask}
              onChange={(e) => setNewTask(e.target.value)}
              placeholder='What needs to be done today?'
              className='w-full bg-transparent border-none outline-none text-foreground placeholder:text-muted-foreground'
              onKeyDown={(e) => {
                if (e.key === 'Enter') addTask();
                if (e.key === 'Escape') {
                  setShowAddForm(false);
                  setNewTask('');
                }
              }}
              autoFocus
            />
            <div className='flex gap-2 mt-3'>
              <button
                onClick={addTask}
                className='px-3 py-1 bg-focus text-focus-foreground text-sm rounded-md hover:bg-focus/90 transition-smooth'
              >
                Add Task
              </button>
              <button
                onClick={() => {
                  setShowAddForm(false);
                  setNewTask('');
                }}
                className='px-3 py-1 text-muted-foreground text-sm hover:text-foreground transition-smooth'
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <button
            onClick={() => setShowAddForm(true)}
            className='flex items-center gap-2 p-4 border-2 border-dashed border-border rounded-lg text-muted-foreground hover:border-focus/30 hover:text-focus transition-smooth'
          >
            <Plus className='h-4 w-4' />
            <span>Add Task</span>
          </button>
        )}
      </div>

      <div className='mt-6 p-3 bg-focus/10 rounded-lg'>
        <p className='text-sm text-focus-foreground'>
          🎯 <strong>AI Tip:</strong> Click the play button to start a focused
          work session for any task.
        </p>
      </div>
    </div>
  );
};
