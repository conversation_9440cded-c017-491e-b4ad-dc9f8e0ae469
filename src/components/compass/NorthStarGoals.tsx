/** @format */

import { useState } from 'react';
import { Plus, Target, ChevronRight } from 'lucide-react';
import { Goal } from '@/components/DailyCompass';

interface NorthStarGoalsProps {
  goals: Goal[];
  onUpdateGoals: (goals: Goal[]) => void;
}

export const NorthStarGoals = ({
  goals,
  onUpdateGoals,
}: NorthStarGoalsProps) => {
  const [newGoal, setNewGoal] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  const addGoal = () => {
    if (newGoal.trim()) {
      const goal: Goal = {
        id: Date.now().toString(),
        title: newGoal.trim(),
        completed: false,
      };
      onUpdateGoals([...goals, goal]);
      setNewGoal('');
      setShowAddForm(false);
    }
  };

  const toggleGoal = (goalId: string) => {
    onUpdateGoals(
      goals.map((goal) =>
        goal.id === goalId ? { ...goal, completed: !goal.completed } : goal,
      ),
    );
  };

  return (
    <div className='bg-card rounded-xl shadow-elegant border border-border p-6 flex flex-col'>
      <div className='flex items-center gap-3 mb-6'>
        <div className='p-2 bg-gradient-primary rounded-lg'>
          <Target className='h-5 w-5 text-primary-foreground' />
        </div>
        <h2 className='text-xl font-semibold text-card-foreground'>
          North Star Goals
        </h2>
      </div>

      <div className='flex-1 space-y-3'>
        {goals.map((goal) => (
          <div
            key={goal.id}
            className={`group p-4 rounded-lg border-2 transition-smooth cursor-pointer hover:shadow-md ${
              goal.completed
                ? 'border-success bg-success/5 text-success'
                : 'border-border bg-background hover:border-primary/30'
            }`}
            onClick={() => toggleGoal(goal.id)}
          >
            <div className='flex items-start justify-between'>
              <div className='flex-1'>
                <h3
                  className={`font-medium ${
                    goal.completed ? 'line-through' : ''
                  }`}
                >
                  {goal.title}
                </h3>
                {goal.description && (
                  <p className='text-sm text-muted-foreground mt-1'>
                    {goal.description}
                  </p>
                )}
              </div>
              <ChevronRight className='h-4 w-4 text-muted-foreground group-hover:text-primary transition-smooth' />
            </div>
          </div>
        ))}

        {showAddForm ? (
          <div className='p-4 border-2 border-dashed border-primary/30 rounded-lg bg-primary/5'>
            <input
              type='text'
              value={newGoal}
              onChange={(e) => setNewGoal(e.target.value)}
              placeholder='Enter your North Star goal...'
              className='w-full bg-transparent border-none outline-none text-foreground placeholder:text-muted-foreground'
              onKeyDown={(e) => {
                if (e.key === 'Enter') addGoal();
                if (e.key === 'Escape') {
                  setShowAddForm(false);
                  setNewGoal('');
                }
              }}
              autoFocus
            />
            <div className='flex gap-2 mt-3'>
              <button
                onClick={addGoal}
                className='px-3 py-1 bg-primary text-primary-foreground text-sm rounded-md hover:bg-primary/90 transition-smooth'
              >
                Add Goal
              </button>
              <button
                onClick={() => {
                  setShowAddForm(false);
                  setNewGoal('');
                }}
                className='px-3 py-1 text-muted-foreground text-sm hover:text-foreground transition-smooth'
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <button
            onClick={() => setShowAddForm(true)}
            className='flex items-center gap-2 p-4 border-2 border-dashed border-border rounded-lg text-muted-foreground hover:border-primary/30 hover:text-primary transition-smooth group'
          >
            <Plus className='h-4 w-4' />
            <span>Add North Star Goal</span>
          </button>
        )}
      </div>

      <div className='mt-6 p-3 bg-accent/20 rounded-lg'>
        <p className='text-sm text-accent-foreground'>
          💡 <strong>AI Tip:</strong> Drag goals to "Today's Path" to break them
          into actionable tasks.
        </p>
      </div>
    </div>
  );
};
