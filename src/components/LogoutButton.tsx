/** @format */

'use client';

import { Button } from '@/components/ui/button';

export function LogoutButton() {
  const handleSignOut = async () => {
    try {
      const response = await fetch('/auth/signout', { method: 'POST' });
      if (response.ok) {
        window.location.href = '/login';
        return;
      }
      // Fallback: reload to re-evaluate auth state if sign-out did not return 2xx
      window.location.reload();
    } catch (error) {
      // Network or unexpected error: reload to ensure auth middleware can correct state
      window.location.reload();
    }
  };

  return (
    <Button variant='outline' onClick={handleSignOut}>
      Sign Out
    </Button>
  );
}
