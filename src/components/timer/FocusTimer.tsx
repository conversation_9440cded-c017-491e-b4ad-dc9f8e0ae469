/** @format */

import { useState, useEffect } from 'react';
import { Play, Pause, Square, RotateCcw } from 'lucide-react';
import { Task } from '@/components/DailyCompass';

interface FocusTimerProps {
  task: Task;
  onComplete: (notes?: string) => void;
  onCancel: () => void;
}

export const FocusTimer = ({ task, onComplete, onCancel }: FocusTimerProps) => {
  const [timeLeft, setTimeLeft] = useState((task.estimatedTime || 25) * 60); // Convert to seconds
  const [isRunning, setIsRunning] = useState(false);
  const [notes, setNotes] = useState('');
  const [showNotes, setShowNotes] = useState(false);

  const totalTime = (task.estimatedTime || 25) * 60;
  const progress = ((totalTime - timeLeft) / totalTime) * 100;

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            setIsRunning(false);
            setShowNotes(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isRunning, timeLeft]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  const handleComplete = () => {
    onComplete(notes);
  };

  const resetTimer = () => {
    setTimeLeft(totalTime);
    setIsRunning(false);
    setShowNotes(false);
    setNotes('');
  };

  if (showNotes) {
    return (
      <div className='min-h-screen bg-gradient-subtle flex items-center justify-center p-6'>
        <div className='max-w-md w-full'>
          <div className='bg-card rounded-xl shadow-elegant border border-border p-8 text-center'>
            <div className='w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6'>
              <div className='text-2xl'>🎉</div>
            </div>

            <h2 className='text-xl font-semibold text-foreground mb-2'>
              Focus Session Complete!
            </h2>
            <p className='text-muted-foreground mb-6'>
              Great work on "{task.title}"
            </p>

            <div className='mb-6'>
              <label className='block text-sm font-medium text-foreground mb-2'>
                How did it go? (optional)
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder='Any key points, insights, or roadblocks to note...'
                className='w-full h-24 bg-input border border-border rounded-lg p-3 text-foreground placeholder:text-muted-foreground resize-none outline-none focus:border-primary transition-smooth'
              />
            </div>

            <div className='flex gap-3'>
              <button
                onClick={handleComplete}
                className='flex-1 py-3 bg-gradient-primary text-primary-foreground rounded-lg font-medium hover:shadow-glow transition-smooth'
              >
                Complete Session
              </button>
              <button
                onClick={resetTimer}
                className='px-4 py-3 border border-border text-muted-foreground hover:text-foreground transition-smooth rounded-lg'
              >
                <RotateCcw className='h-4 w-4' />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gradient-subtle flex items-center justify-center p-6'>
      <div className='max-w-md w-full'>
        <div className='bg-card rounded-xl shadow-elegant border border-border p-8 text-center'>
          {/* Task Info */}
          <div className='mb-8'>
            <h2 className='text-xl font-semibold text-foreground mb-2'>
              {task.title}
            </h2>
            {task.description && (
              <p className='text-muted-foreground text-sm'>
                {task.description}
              </p>
            )}
          </div>

          {/* Timer Circle */}
          <div className='relative w-48 h-48 mx-auto mb-8'>
            <svg
              className='w-full h-full transform -rotate-90'
              viewBox='0 0 100 100'
            >
              {/* Background circle */}
              <circle
                cx='50'
                cy='50'
                r='45'
                stroke='hsl(var(--muted))'
                strokeWidth='4'
                fill='transparent'
              />
              {/* Progress circle */}
              <circle
                cx='50'
                cy='50'
                r='45'
                stroke='hsl(var(--focus))'
                strokeWidth='4'
                fill='transparent'
                strokeDasharray={`${2 * Math.PI * 45}`}
                strokeDashoffset={`${2 * Math.PI * 45 * (1 - progress / 100)}`}
                className='transition-all duration-1000 ease-out'
                style={{
                  filter:
                    timeLeft <= 60
                      ? 'drop-shadow(0 0 8px hsl(var(--focus)))'
                      : 'none',
                }}
              />
            </svg>

            {/* Timer display */}
            <div className='absolute inset-0 flex items-center justify-center'>
              <div className='text-center'>
                <div
                  className={`text-4xl font-mono font-bold transition-smooth ${
                    timeLeft <= 60
                      ? 'text-focus animate-pulse-glow'
                      : 'text-foreground'
                  }`}
                >
                  {formatTime(timeLeft)}
                </div>
                <div className='text-sm text-muted-foreground mt-1'>
                  {isRunning ? 'Focus Mode' : 'Paused'}
                </div>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className='flex justify-center gap-4 mb-6'>
            <button
              onClick={() => setIsRunning(!isRunning)}
              className='p-4 bg-gradient-focus text-focus-foreground rounded-full hover:shadow-glow transition-smooth'
            >
              {isRunning ? (
                <Pause className='h-6 w-6' />
              ) : (
                <Play className='h-6 w-6' />
              )}
            </button>

            <button
              onClick={resetTimer}
              className='p-4 border border-border text-muted-foreground hover:text-foreground transition-smooth rounded-full'
            >
              <RotateCcw className='h-6 w-6' />
            </button>

            <button
              onClick={onCancel}
              className='p-4 border border-border text-muted-foreground hover:text-destructive transition-smooth rounded-full'
            >
              <Square className='h-6 w-6' />
            </button>
          </div>

          {timeLeft <= 60 && timeLeft > 0 && (
            <div className='p-3 bg-focus/10 border border-focus/20 rounded-lg animate-breathe'>
              <p className='text-sm text-focus-foreground font-medium'>
                ⏰ Almost there! Less than a minute remaining.
              </p>
            </div>
          )}

          {!isRunning && timeLeft === totalTime && (
            <div className='p-3 bg-primary/10 border border-primary/20 rounded-lg'>
              <p className='text-sm text-primary'>
                🎯 Ready to start your focus session?
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
