/** @format */

'use client';
import { useState, useEffect } from 'react';
import { NorthStarGoals } from '@/components/compass/NorthStarGoals';
import { TodaysPath } from '@/components/compass/TodaysPath';
import { TheLog } from '@/components/compass/TheLog';
import { MorningKickoff } from '@/components/phases/MorningKickoff';
import { EveningReview } from '@/components/phases/EveningReview';
import { FocusTimer } from '@/components/timer/FocusTimer';
import { Goal, Task, LogEntry } from '@/data/types';

export type Phase = 'morning' | 'active' | 'focus' | 'evening';


const DailyCompass = () => {
  const [currentPhase, setCurrentPhase] = useState<Phase>('morning');
  const [goals, setGoals] = useState<Goal[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [focusTask, setFocusTask] = useState<Task | null>(null);
  const [isFirstTime, setIsFirstTime] = useState(true);

  // Initialize with sample data
  useEffect(() => {

    const fetchGoals = async () => {
      const response = await fetch('/api/goals');
      const data = await response.json();
      setGoals(data);
    };
    fetchGoals();

    // get goals from api/goals
    // const response = await fetch('/api/goals');
    // const data = await response.json();
    // setGoals(data);

    const sampleTasks: Task[] = [
      {
        id: '1',
        title: 'Draft campaign strategy document',
        goal_id: '1',
        status: 'doing',
      },
      {
        id: '2',
        title: 'Review financial data',
        goal_id: '2',
        status: 'doing',
      },
    ];

    const sampleLog: LogEntry[] = [
      {
        id: '1',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
        content:
          'Remember to follow up with the design team about the campaign visuals. Also need to schedule a meeting with finance for the Q3 numbers review.',
        type: 'note',
      },
    ];

    setTasks(sampleTasks);
    setLogEntries(sampleLog);
  }, []);

  const addLogEntry = (
    content: string,
    type: LogEntry['type'],
    taskId?: string,
  ) => {
    const newEntry: LogEntry = {
      id: Date.now().toString(),
      timestamp: new Date(),
      content,
      type,
      taskId,
    };
    setLogEntries((prev) => [newEntry, ...prev]);
  };

  const startFocusSession = (task: Task) => {
    setFocusTask(task);
    setCurrentPhase('focus');
    setTasks((prev) =>
      prev.map((t) => (t.id === task.id ? { ...t, inProgress: true } : t)),
    );
  };

  const completeFocusSession = (notes?: string) => {
    if (focusTask) {
      addLogEntry(
        `Focus session completed for "${focusTask.title}"${
          notes ? `. Notes: ${notes}` : ''
        }`,
        'focus',
        focusTask.id,
      );
      setTasks((prev) =>
        prev.map((t) =>
          t.id === focusTask.id ? { ...t, inProgress: false } : t,
        ),
      );
    }
    setFocusTask(null);
    setCurrentPhase('active');
  };

  const completeTask = (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId);
    if (task) {
      setTasks((prev) =>
        prev.map((t) => (t.id === taskId ? { ...t, completed: true } : t)),
      );
      addLogEntry(`Completed: ${task.title}`, 'completion', taskId);
    }
  };

  const startDay = () => {
    setCurrentPhase('active');
    setIsFirstTime(false);
    addLogEntry('Day started with morning planning session', 'note');
  };

  const startEvening = () => {
    setCurrentPhase('evening');
  };

  if (currentPhase === 'morning' && isFirstTime) {
    return (
      <MorningKickoff
        previousNotes={logEntries.filter((entry) => entry.type === 'note')}
        goals={goals}
        onStartDay={startDay}
        onAddTask={(task) => setTasks((prev) => [...prev, task])}
      />
    );
  }

  if (currentPhase === 'focus' && focusTask) {
    return (
      <FocusTimer
        task={focusTask}
        onComplete={completeFocusSession}
        onCancel={() => {
          setCurrentPhase('active');
          setFocusTask(null);
          setTasks((prev) =>
            prev.map((t) =>
              t.id === focusTask.id ? { ...t, inProgress: false } : t,
            ),
          );
        }}
      />
    );
  }

  if (currentPhase === 'evening') {
    return (
      <EveningReview
        tasks={tasks}
        logEntries={logEntries}
        onAddReflection={(content) => addLogEntry(content, 'reflection')}
        onBackToActive={() => setCurrentPhase('active')}
      />
    );
  }

  return (
    <div className='min-h-screen bg-gradient-subtle'>
      <div className='max-w-7xl mx-auto p-6'>
        {/* Header */}
        <div className='mb-8 text-center'>
          <h1 className='text-3xl font-bold text-foreground mb-2'>
            Daily Compass
          </h1>
          <p className='text-muted-foreground'>
            Your intelligent workspace for deep work and mindful productivity
          </p>
          <div className='mt-4 flex justify-center gap-4'>
            <button
              onClick={startEvening}
              className='px-4 py-2 text-sm bg-accent hover:bg-accent/80 text-accent-foreground rounded-lg transition-smooth'
            >
              Evening Review
            </button>
          </div>
        </div>

        {/* Three-pane layout */}
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]'>
          <NorthStarGoals goals={goals} onUpdateGoals={setGoals} />
          <TodaysPath
            tasks={tasks}
            onCompleteTask={completeTask}
            onStartFocus={startFocusSession}
            onUpdateTasks={setTasks}
          />
          <TheLog logEntries={logEntries} onAddEntry={addLogEntry} />
        </div>
      </div>
    </div>
  );
};

export default DailyCompass;
