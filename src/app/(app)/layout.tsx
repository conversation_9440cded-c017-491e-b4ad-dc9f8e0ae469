/** @format */

import { requireUser } from '@/lib/supabase/auth-helpers';
import { UserMenu } from '@/components/UserMenu';

export default async function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  await requireUser();
  return (
    <div className='min-h-screen'>
      <header className='sticky top-0 z-10 border-b bg-background/80 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
        <div className='mx-auto flex max-w-5xl items-center justify-end gap-2 p-3'>
          <UserMenu />
        </div>
      </header>
      <main>{children}</main>
    </div>
  );
}
