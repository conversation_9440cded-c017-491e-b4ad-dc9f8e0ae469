/** @format */

import { NextResponse, type NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import {
  createTask,
  listTasksForDate,
  type CreateTaskInput,
} from '@/data/tasks';
import { toHttpError } from '@/lib/http-errors';
import { AuthError, ValidationError } from '@/lib/errors';
import { logger, getOrCreateCorrelationId } from '@/lib/logger';
import { z } from 'zod';

const CreateTaskSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  goal_id: z.string().uuid().optional(),
  planned_for_date: z.string().date().optional(),
});

export async function GET(request: NextRequest) {
  const correlationId = getOrCreateCorrelationId(request.headers);
  const log = logger.child({
    route: 'api/tasks',
    method: 'GET',
    correlationId,
  });

  try {
    const supabase = createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new AuthError('Unauthorized');
    }

    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date') || undefined;
    const tasks = await listTasksForDate(supabase, user.id, date);

    const res = NextResponse.json({ tasks });
    res.headers.set('x-request-id', correlationId);
    return res;
  } catch (e) {
    log.error('tasks_get_failed', e);
    const { status, body } = toHttpError(e, correlationId);
    const res = NextResponse.json(body, { status });
    res.headers.set('x-request-id', correlationId);
    return res;
  }
}

export async function POST(request: NextRequest) {
  const correlationId = getOrCreateCorrelationId(request.headers);
  const log = logger.child({
    route: 'api/tasks',
    method: 'POST',
    correlationId,
  });

  try {
    const supabase = createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();
    if (!user) {
      throw new AuthError('Unauthorized');
    }

    const rawBody = await request.json();
    const parseResult = CreateTaskSchema.safeParse(rawBody);

    if (!parseResult.success) {
      throw new ValidationError('Invalid request body', {
        issues: parseResult.error.issues,
      });
    }

    await createTask(supabase, user.id, parseResult.data as CreateTaskInput);

    const res = NextResponse.json({ ok: true });
    res.headers.set('x-request-id', correlationId);
    return res;
  } catch (e) {
    log.error('tasks_post_failed', e);
    const { status, body } = toHttpError(e, correlationId);
    const res = NextResponse.json(body, { status });
    res.headers.set('x-request-id', correlationId);
    return res;
  }
}
