export const runtime = 'edge';

function getEnvelopeEndpointFromDsn(dsn: string): string | null {
  try {
    const url = new URL(dsn);
    const host = url.host;
    const pathParts = url.pathname.split('/').filter(Boolean);
    const projectId = pathParts[pathParts.length - 1];
    if (!host || !projectId) return null;
    return `https://${host}/api/${projectId}/envelope/`;
  } catch {
    return null;
  }
}

export async function POST(request: Request) {
  const body = await request.text();
  const [headerLine] = body.split('\n');

  // Header is a JSON object as the first line of the envelope
  let dsn: string | undefined;
  try {
    const header = JSON.parse(headerLine);
    dsn = header.dsn;
  } catch {
    // ignore
  }

  const endpoint = dsn ? getEnvelopeEndpointFromDsn(dsn) : null;
  if (!endpoint) {
    return new Response(null, { status: 400 });
  }

  const res = await fetch(endpoint, {
    method: 'POST',
    body,
    headers: { 'Content-Type': 'application/x-sentry-envelope' },
  });

  return new Response(null, { status: res.status });
}
