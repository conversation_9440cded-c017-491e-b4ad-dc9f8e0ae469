import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { AuthError, ValidationError } from '@/lib/errors';
import { createGoal, listGoals, type CreateGoalInput } from '@/data/goals';
import { z } from 'zod';
import { getOrCreateCorrelationId, logger } from '@/lib/logger';
import { withCorrelationId } from '@/lib/logger/context';
import { toHttpError } from '@/lib/http-errors';

const CreateGoalSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  timeframe: z.string().optional(),
  exclude_from_ai: z.boolean().optional(),
});

export async function GET(request: NextRequest) {
  const correlationId = getOrCreateCorrelationId(request.headers);
  return withCorrelationId(correlationId, async () => {
    const log = logger.child({ route: 'api/goals', method: 'GET' });
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new AuthError('Unauthorized');
      }
      const data = await listGoals(supabase, user.id);
      const res = NextResponse.json(data);
      res.headers.set('x-request-id', correlationId);
      return res;
    } catch (e) {
      log.error('goals_get_failed', e);
      const { status, body } = toHttpError(e, correlationId);
      const res = NextResponse.json(body, { status });
      res.headers.set('x-request-id', correlationId);
      return res;
    }
  });
}

export async function POST(request: NextRequest) {
  const correlationId = getOrCreateCorrelationId(request.headers);
  return withCorrelationId(correlationId, async () => {
    const log = logger.child({ route: 'api/goals', method: 'POST' });
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        throw new AuthError('Unauthorized');
      }
      const rawBody = await request.json();
      const parseResult = CreateGoalSchema.safeParse(rawBody);
      if (!parseResult.success) {
        throw new ValidationError('Invalid request body', {
          issues: parseResult.error.issues,
        });
      }
      if (!parseResult.data.id) {
        parseResult.data.id = crypto.randomUUID();
      }
      const data = await createGoal(
        supabase,
        user.id,
        parseResult.data as CreateGoalInput,
      );
      const res = NextResponse.json(data);
      res.headers.set('x-request-id', correlationId);
      return res;
    } catch (e) {
      log.error('goals_post_failed', e);
      const { status, body } = toHttpError(e, correlationId);
      const res = NextResponse.json(body, { status });
      res.headers.set('x-request-id', correlationId);
      return res;
    }
  });
}
