/** @format */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Core productivity palette */
    --background: 45 8% 98%;
    --foreground: 160 20% 12%;

    --card: 0 0% 100%;
    --card-foreground: 160 20% 12%;

    --popover: 0 0% 100%;
    --popover-foreground: 160 20% 12%;

    /* Forest green primary for focus and clarity */
    --primary: 160 84% 16%;
    --primary-foreground: 45 8% 98%;
    --primary-glow: 160 60% 25%;

    /* Warm earth tones for secondary */
    --secondary: 45 12% 92%;
    --secondary-foreground: 160 20% 12%;

    /* Subtle muted tones */
    --muted: 45 8% 95%;
    --muted-foreground: 45 4% 45%;

    /* Sage accent for gentle emphasis */
    --accent: 160 20% 88%;
    --accent-foreground: 160 84% 16%;

    /* Focus amber for timer states */
    --focus: 38 92% 50%;
    --focus-foreground: 160 20% 12%;
    --focus-glow: 38 70% 60%;

    /* Success green for completed tasks */
    --success: 142 76% 36%;
    --success-foreground: 45 8% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 45 8% 98%;

    --border: 45 12% 88%;
    --input: 45 12% 92%;
    --ring: 160 84% 16%;

    /* Gradients for depth and warmth */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--primary)),
      hsl(var(--primary-glow))
    );
    --gradient-focus: linear-gradient(
      135deg,
      hsl(var(--focus)),
      hsl(var(--focus-glow))
    );
    --gradient-subtle: linear-gradient(
      180deg,
      hsl(var(--background)),
      hsl(var(--muted))
    );

    /* Shadows for elevation */
    --shadow-elegant: 0 4px 20px -4px hsl(var(--primary) / 0.15);
    --shadow-focus: 0 0 0 3px hsl(var(--focus) / 0.2);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.3);

    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-focus: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode with deep forest theme */
    --background: 160 25% 8%;
    --foreground: 45 25% 92%;

    --card: 160 20% 10%;
    --card-foreground: 45 25% 92%;

    --popover: 160 20% 10%;
    --popover-foreground: 45 25% 92%;

    --primary: 160 60% 75%;
    --primary-foreground: 160 25% 8%;
    --primary-glow: 160 50% 65%;

    --secondary: 160 15% 15%;
    --secondary-foreground: 45 25% 92%;

    --muted: 160 12% 12%;
    --muted-foreground: 45 8% 65%;

    --accent: 160 25% 20%;
    --accent-foreground: 160 60% 75%;

    --focus: 38 85% 55%;
    --focus-foreground: 160 25% 8%;
    --focus-glow: 38 75% 65%;

    --success: 142 70% 45%;
    --success-foreground: 160 25% 8%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 45 25% 92%;

    --border: 160 15% 20%;
    --input: 160 15% 15%;
    --ring: 160 60% 75%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--primary)),
      hsl(var(--primary-glow))
    );
    --gradient-focus: linear-gradient(
      135deg,
      hsl(var(--focus)),
      hsl(var(--focus-glow))
    );
    --gradient-subtle: linear-gradient(
      180deg,
      hsl(var(--background)),
      hsl(var(--muted))
    );

    /* Dark mode shadows */
    --shadow-elegant: 0 4px 20px -4px hsl(0 0% 0% / 0.4);
    --shadow-focus: 0 0 0 3px hsl(var(--focus) / 0.3);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.2);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
