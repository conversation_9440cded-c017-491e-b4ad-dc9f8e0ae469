/** @format */

import { NextRequest } from 'next/server';
import * as route from '@/app/auth/callback/route';

jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => ({
    auth: {
      exchangeCodeForSession: jest.fn(),
      getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'u1' } } }),
    },
  })),
}));

jest.mock('@/services/users', () => ({
  bootstrapUser: jest.fn().mockResolvedValue(undefined),
}));

describe('auth callback route', () => {
  function buildRequest(search: string) {
    const url = `http://localhost/auth/callback${search}`;
    return new NextRequest(new Request(url));
  }

  it('exchanges code and boots user, then redirects home', async () => {
    const req = buildRequest('?code=abc');
    const res = await route.GET(req);
    expect(res.status).toBe(307);
    expect(res.headers.get('location')).toBe('http://localhost/');
  });

  it('redirects home even without code', async () => {
    const req = buildRequest('');
    const res = await route.GET(req);
    expect(res.status).toBe(307);
    expect(res.headers.get('location')).toBe('http://localhost/');
  });
});
