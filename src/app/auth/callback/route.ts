/** @format */

import { NextResponse, type NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { bootstrapUser } from '@/services/users';
import { toHttpError } from '@/lib/http-errors';
import { logger, getOrCreateCorrelationId } from '@/lib/logger';

export async function GET(request: NextRequest) {
  const correlationId = getOrCreateCorrelationId(request.headers);
  const log = logger.child({ route: 'auth/callback', correlationId });

  try {
    const supabase = createClient();
    const code = request.nextUrl.searchParams.get('code');

    if (code) {
      await supabase.auth.exchangeCodeForSession(code);
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (user) await bootstrapUser(supabase, user.id);
    }

    const res = NextResponse.redirect(new URL('/', request.url));
    res.headers.set('x-request-id', correlationId);
    return res;
  } catch (e) {
    log.error('auth_callback_failed', e);
    const { status, body } = toHttpError(e, correlationId);
    const res = NextResponse.json(body, { status });
    res.headers.set('x-request-id', correlationId);
    return res;
  }
}
