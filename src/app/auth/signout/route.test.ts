/** @format */

import * as route from '@/app/auth/signout/route';
import { NextRequest } from 'next/server';

jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => ({
    auth: {
      signOut: jest.fn().mockResolvedValue(undefined),
    },
  })),
}));

describe('auth signout route', () => {
  it('returns 204 after signOut', async () => {
    const res = await route.POST(
      new NextRequest('http://localhost/auth/signout'),
    );
    expect(res.status).toBe(204);
  });
});
