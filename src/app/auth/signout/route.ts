/** @format */

import { NextResponse, type NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { toHttpError } from '@/lib/http-errors';
import { logger, getOrCreateCorrelationId } from '@/lib/logger';

export async function POST(request: NextRequest) {
  const correlationId = getOrCreateCorrelationId(request.headers);
  const log = logger.child({ route: 'auth/signout', correlationId });

  try {
    const supabase = createClient();
    await supabase.auth.signOut();

    const res = new NextResponse(null, { status: 204 });
    res.headers.set('x-request-id', correlationId);
    return res;
  } catch (e) {
    log.error('auth_signout_failed', e);
    const { status, body } = toHttpError(e, correlationId);
    const res = NextResponse.json(body, { status });
    res.headers.set('x-request-id', correlationId);
    return res;
  }
}
