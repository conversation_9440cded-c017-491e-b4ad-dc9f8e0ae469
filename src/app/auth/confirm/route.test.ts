/** @format */

import { NextRequest } from 'next/server';
import * as route from '@/app/auth/confirm/route';

jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => ({
    auth: {
      verifyOtp: jest.fn().mockResolvedValue({ error: null }),
    },
  })),
}));

jest.mock('@/lib/logger', () => ({
  logger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      error: jest.fn(),
    })),
  },
  getOrCreateCorrelationId: jest.fn(() => 'test-correlation-id'),
}));

describe('auth confirm route', () => {
  function buildRequest(search: string) {
    const url = `http://localhost/auth/confirm${search}`;
    return new NextRequest(new Request(url));
  }

  it('redirects to next when token and type provided', async () => {
    const req = buildRequest('?token_hash=tok&type=magiclink&next=%2F');
    await expect(route.GET(req)).rejects.toMatchObject({
      message: expect.stringContaining('REDIRECT:/'),
    });
  });

  it('redirects to /login?error=auth_error when missing params', async () => {
    const req = buildRequest('');
    await expect(route.GET(req)).rejects.toMatchObject({
      message: expect.stringContaining('REDIRECT:/login?error=auth_error'),
    });
  });
});
