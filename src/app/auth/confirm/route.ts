/** @format */

import { type EmailOtpType } from '@supabase/supabase-js';
import { type NextRequest } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { logger, getOrCreateCorrelationId } from '@/lib/logger';

export async function GET(request: NextRequest) {
  const correlationId = getOrCreateCorrelationId(request.headers);
  const log = logger.child({ route: 'auth/confirm', correlationId });

  const { searchParams } = new URL(request.url);
  const token_hash = searchParams.get('token_hash');
  const type = searchParams.get('type') as EmailOtpType | null;
  const next = searchParams.get('next') ?? '/';

  if (token_hash && type) {
    const supabase = createClient();

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    });

    if (!error) {
      log.info('auth_confirm_success', { type, next });
      redirect(next);
    } else {
      log.error('auth_confirm_verify_failed', { error, type, correlationId });
    }
  } else {
    log.error('auth_confirm_missing_params', {
      hasTokenHash: !!token_hash,
      hasType: !!type,
      correlationId,
    });
  }

  redirect('/login?error=auth_error');
}
