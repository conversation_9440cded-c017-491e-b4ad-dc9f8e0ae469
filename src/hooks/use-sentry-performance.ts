import { useCallback } from 'react';
import { trackInteraction } from '@/lib/sentry';

/**
 * Hook to track user interactions
 */
export function useSentryInteractionTracking() {
  const trackClick = useCallback(
    (
      elementName: string,
      context?: Record<string, string | number | boolean>,
    ) => {
      trackInteraction(`Click: ${elementName}`, context);
    },
    [],
  );

  const trackFormSubmission = useCallback(
    (formName: string, context?: Record<string, string | number | boolean>) => {
      return trackInteraction(`Form Submit: ${formName}`, context);
    },
    [],
  );

  const trackCustomAction = useCallback(
    (
      actionName: string,
      context?: Record<string, string | number | boolean>,
    ) => {
      return trackInteraction(actionName, context);
    },
    [],
  );

  return {
    trackClick,
    trackFormSubmission,
    trackCustomAction,
  };
}
