import { useCallback, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { trackPageView, trackInteraction } from '@/lib/sentry';

/**
 * Hook to track page views automatically
 */
export function useSentryPageTracking() {
  const pathname = usePathname();
  
  useEffect(() => {
    // Track page view when component mounts or pathname changes
    trackPageView(pathname);
  }, [pathname]);
}

/**
 * Hook to track user interactions
 */
export function useSentryInteractionTracking() {
  const trackClick = useCallback((elementName: string, context?: Record<string, string | number | boolean>) => {
    trackInteraction(`Click: ${elementName}`, context);
  }, []);
  
  const trackFormSubmission = useCallback((formName: string, context?: Record<string, string | number | boolean>) => {
    return trackInteraction(`Form Submit: ${formName}`, context);
  }, []);
  
  const trackCustomAction = useCallback((actionName: string, context?: Record<string, string | number | boolean>) => {
    return trackInteraction(actionName, context);
  }, []);
  
  return {
    trackClick,
    trackFormSubmission,
    trackCustomAction,
  };
}