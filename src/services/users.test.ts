/** @format */

import { bootstrapUser } from '@/services/users';
import { upsertProfile, upsertSettings } from '@/data/profiles';
import { BootstrapError, RepositoryError } from '@/lib/errors';

// Mock the data layer functions
jest.mock('@/data/profiles', () => ({
  upsertProfile: jest.fn(),
  upsertSettings: jest.fn(),
}));

// Mock the logger to avoid console output during tests
jest.mock('@/lib/logger', () => ({
  logger: {
    child: jest.fn(() => ({
      warn: jest.fn(),
      info: jest.fn(),
      error: jest.fn(),
    })),
  },
}));

const mockUpsertProfile = upsertProfile as jest.MockedFunction<
  typeof upsertProfile
>;
const mockUpsertSettings = upsertSettings as jest.MockedFunction<
  typeof upsertSettings
>;

const mockDb = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        maybeSingle: jest.fn(),
      })),
    })),
    delete: jest.fn(() => ({
      eq: jest.fn(),
    })),
  })),
} as any;

describe('bootstrapUser', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default: profile doesn't exist before bootstrap
    mockDb.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          maybeSingle: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      }),
      delete: jest.fn().mockReturnValue({
        eq: jest.fn().mockResolvedValue({ error: null }),
      }),
    });
  });

  it('successfully bootstraps user when both operations succeed', async () => {
    mockUpsertProfile.mockResolvedValue();
    mockUpsertSettings.mockResolvedValue();

    await expect(bootstrapUser(mockDb, 'user-1')).resolves.toBeUndefined();

    expect(mockUpsertProfile).toHaveBeenCalledWith(mockDb, 'user-1');
    expect(mockUpsertSettings).toHaveBeenCalledWith(mockDb, 'user-1');
  });

  it('throws BootstrapError when profile creation fails', async () => {
    const profileError = new RepositoryError('Profile creation failed');
    mockUpsertProfile.mockRejectedValue(profileError);

    await expect(bootstrapUser(mockDb, 'user-1')).rejects.toThrow(
      BootstrapError,
    );

    expect(mockUpsertProfile).toHaveBeenCalledWith(mockDb, 'user-1');
    expect(mockUpsertSettings).not.toHaveBeenCalled();
  });

  it('throws BootstrapError when settings creation fails', async () => {
    const settingsError = new RepositoryError('Settings creation failed');
    mockUpsertProfile.mockResolvedValue();
    mockUpsertSettings.mockRejectedValue(settingsError);

    await expect(bootstrapUser(mockDb, 'user-1')).rejects.toThrow(
      BootstrapError,
    );

    expect(mockUpsertProfile).toHaveBeenCalledWith(mockDb, 'user-1');
    expect(mockUpsertSettings).toHaveBeenCalledWith(mockDb, 'user-1');
  });

  it('cleans up created profile when settings fail and profile did not exist before', async () => {
    const settingsError = new RepositoryError('Settings creation failed');
    mockUpsertProfile.mockResolvedValue();
    mockUpsertSettings.mockRejectedValue(settingsError);

    // Profile didn't exist before (default mock setup)
    const mockEq = jest.fn().mockResolvedValue({ error: null });
    mockDb.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          maybeSingle: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      }),
      delete: jest.fn().mockReturnValue({
        eq: mockEq,
      }),
    });

    await expect(bootstrapUser(mockDb, 'user-1')).rejects.toThrow(
      BootstrapError,
    );

    expect(mockEq).toHaveBeenCalledWith('user_id', 'user-1');
  });

  it('does not clean up profile when it existed before bootstrap', async () => {
    const settingsError = new RepositoryError('Settings creation failed');
    mockUpsertProfile.mockResolvedValue();
    mockUpsertSettings.mockRejectedValue(settingsError);

    // Profile existed before bootstrap
    const mockEq = jest.fn();
    mockDb.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          maybeSingle: jest.fn().mockResolvedValue({
            data: { user_id: 'user-1' },
            error: null,
          }),
        }),
      }),
      delete: jest.fn().mockReturnValue({
        eq: mockEq,
      }),
    });

    await expect(bootstrapUser(mockDb, 'user-1')).rejects.toThrow(
      BootstrapError,
    );

    expect(mockEq).not.toHaveBeenCalled();
  });

  it('handles cleanup failure gracefully', async () => {
    const settingsError = new RepositoryError('Settings creation failed');
    const cleanupError = new Error('Cleanup failed');

    mockUpsertProfile.mockResolvedValue();
    mockUpsertSettings.mockRejectedValue(settingsError);

    // Profile didn't exist, but cleanup fails
    const mockEq = jest.fn().mockRejectedValue(cleanupError);
    mockDb.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          maybeSingle: jest.fn().mockResolvedValue({
            data: null,
            error: null,
          }),
        }),
      }),
      delete: jest.fn().mockReturnValue({
        eq: mockEq,
      }),
    });

    // Should still throw the original BootstrapError, not the cleanup error
    await expect(bootstrapUser(mockDb, 'user-1')).rejects.toThrow(
      BootstrapError,
    );
    await expect(bootstrapUser(mockDb, 'user-1')).rejects.toThrow(
      'User bootstrap failed',
    );
  });

  it('assumes profile existed when existence check fails', async () => {
    const settingsError = new RepositoryError('Settings creation failed');
    const existenceCheckError = new Error('DB connection failed');

    mockUpsertProfile.mockResolvedValue();
    mockUpsertSettings.mockRejectedValue(settingsError);

    // Existence check fails
    const mockEq = jest.fn();
    mockDb.from.mockReturnValue({
      select: jest.fn().mockReturnValue({
        eq: jest.fn().mockReturnValue({
          maybeSingle: jest.fn().mockResolvedValue({
            data: null,
            error: existenceCheckError,
          }),
        }),
      }),
      delete: jest.fn().mockReturnValue({
        eq: mockEq,
      }),
    });

    await expect(bootstrapUser(mockDb, 'user-1')).rejects.toThrow(
      BootstrapError,
    );

    // Should not attempt cleanup when existence check fails (assumes existed)
    expect(mockEq).not.toHaveBeenCalled();
  });
});
