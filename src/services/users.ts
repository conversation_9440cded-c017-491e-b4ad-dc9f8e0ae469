/** @format */

import type { Db } from '@/data/types';
import { upsertProfile, upsertSettings } from '@/data/profiles';
import { BootstrapError, RepositoryError } from '@/lib/errors';
import { logger } from '@/lib/logger';

export async function bootstrapUser(db: Db, userId: string): Promise<void> {
  const log = logger.child({
    module: 'services/users',
    fn: 'bootstrapUser',
    userId,
  });

  let profileSuccess = false;
  let settingsSuccess = false;
  let profileExistedBefore = false;

  try {
    // Check if profile already exists before attempting upsert so cleanup is safe
    {
      const { data: existingProfile, error: existenceCheckError } = await db
        .from('profiles')
        .select('user_id')
        .eq('user_id', userId)
        .maybeSingle();

      if (existenceCheckError) {
        // Be conservative: if we cannot determine existence, assume it existed to avoid data loss
        log.warn(
          'bootstrap_profile_existence_check_failed',
          existenceCheckError,
        );
        profileExistedBefore = true;
      } else {
        profileExistedBefore = Boolean(existingProfile);
      }
    }

    // Try profile first
    await upsertProfile(db, userId);
    profileSuccess = true;

    // Then settings
    await upsertSettings(db, userId);
    settingsSuccess = true;
  } catch (e) {
    // Compensating cleanup on partial failure
    if (profileSuccess && !settingsSuccess && !profileExistedBefore) {
      log.warn('bootstrap_partial_failure_attempting_cleanup', {
        profileSuccess,
        settingsSuccess,
        profileExistedBefore,
      });

      try {
        // Attempt to clean up the profile that was successfully created
        await db.from('profiles').delete().eq('user_id', userId);
        log.info('bootstrap_cleanup_successful');
      } catch (cleanupError) {
        log.error('bootstrap_cleanup_failed', cleanupError);
        // Don't throw cleanup error - original error is more important
      }
    }

    if (e instanceof RepositoryError) {
      throw new BootstrapError('User bootstrap failed', e, {
        userId,
        profileSuccess,
        settingsSuccess,
        profileExistedBefore,
      });
    }
    throw e;
  }
}
