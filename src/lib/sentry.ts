import * as Sentry from '@sentry/nextjs';

/**
 * Capture a custom event with additional context
 */
export function captureEvent(message: string, context?: Record<string, unknown>) {
  Sentry.addBreadcrumb({
    message,
    level: 'info',
    data: context,
  });
}

/**
 * Set user context for error tracking
 */
export function setUserContext(user: {
  id?: string;
  email?: string;
  username?: string;
  [key: string]: unknown;
}) {
  Sentry.setUser(user);
}

/**
 * Add tags to the current scope
 */
export function setTags(tags: Record<string, string>) {
  Sentry.setTags(tags);
}

/**
 * Add context to the current scope
 */
export function setContext(key: string, context: Record<string, unknown>) {
  Sentry.setContext(key, context);
}

/**
 * Start a performance span
 */
export function startSpan(name: string, op?: string) {
  return Sentry.startSpan({
    name,
    op: op || 'navigation',
  }, () => {
    // Return a function that can be called to finish the span
    return {
      finish: () => {
        // Span is automatically finished when the callback completes
      }
    };
  });
}

/**
 * Track page view performance
 */
export function trackPageView(pageName: string) {
  return Sentry.startSpan({
    name: `Page: ${pageName}`,
    op: 'navigation',
  }, (span) => {
    // Finish the span when the page is fully loaded
    if (typeof window !== 'undefined') {
      window.addEventListener('load', () => {
        span?.end();
      });
    }
    
    return span;
  });
}

/**
 * Track user interactions (clicks, form submissions, etc.)
 */
export function trackInteraction(actionName: string, context?: Record<string, string | number | boolean>) {
  return Sentry.startSpan({
    name: `Action: ${actionName}`,
    op: 'interaction',
    attributes: context || {},
  }, (span) => {
    return span;
  });
}

/**
 * Measure function execution time
 */
export async function measureAsync<T>(
  operationName: string,
  fn: () => Promise<T>,
  context?: Record<string, string | number | boolean>
): Promise<T> {
  return await Sentry.startSpan({
    name: `Operation: ${operationName}`,
    op: 'task',
    attributes: context || {},
  }, async (span) => {
    try {
      const result = await fn();
      span?.setStatus({ code: 1 }); // OK status
      return result;
    } catch (error) {
      span?.setStatus({ code: 2 }); // ERROR status
      Sentry.captureException(error);
      throw error;
    }
  });
}

/**
 * Measure synchronous function execution time
 */
export function measureSync<T>(
  operationName: string,
  fn: () => T,
  context?: Record<string, string | number | boolean>
): T {
  return Sentry.startSpan({
    name: `Operation: ${operationName}`,
    op: 'task',
    attributes: context || {},
  }, (span) => {
    try {
      const result = fn();
      span?.setStatus({ code: 1 }); // OK status
      return result;
    } catch (error) {
      span?.setStatus({ code: 2 }); // ERROR status
      Sentry.captureException(error);
      throw error;
    }
  });
}