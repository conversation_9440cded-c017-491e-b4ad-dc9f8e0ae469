/** @format */

import * as helpers from '@/lib/supabase/auth-helpers';

jest.mock('next/navigation', () => ({
  redirect: (url: string) => {
    const err = new Error(`REDIRECT:${url}`);
    // @ts-expect-error: attach test-only flag to identify redirect exceptions
    err.__isRedirect = true;
    throw err;
  },
}));

jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(),
}));

describe('auth helpers', () => {
  const { createServerClient } = jest.requireMock('@supabase/ssr');

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it('requireUser redirects when no user', async () => {
    createServerClient.mockReturnValue({
      auth: { getUser: jest.fn().mockResolvedValue({ data: { user: null } }) },
    });

    await expect(helpers.requireUser()).rejects.toMatchObject({
      message: expect.stringContaining('REDIRECT:/login'),
    });
  });

  it('requireUser returns user when present', async () => {
    createServerClient.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'u1' } } }),
      },
    });

    const result = await helpers.requireUser();
    expect(result.user).toEqual({ id: 'u1' });
  });

  it('getUserOptional returns user or null', async () => {
    createServerClient.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'u1' } } }),
      },
    });

    const { user } = await helpers.getUserOptional();
    expect(user).toEqual({ id: 'u1' });
  });
});
