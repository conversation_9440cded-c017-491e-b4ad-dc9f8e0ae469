/** @format */

import * as server from '@/lib/supabase/server';

jest.mock('next/headers', () => ({
  cookies: async () => ({
    getAll: () => [],
    set: () => undefined,
  }),
}));

jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => ({
    auth: {},
  })),
}));

describe('supabase server client', () => {
  it('creates a server client without throwing', () => {
    expect(() => server.createClient()).not.toThrow();
  });
});
