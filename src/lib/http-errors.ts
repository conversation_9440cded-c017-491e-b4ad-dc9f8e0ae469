/** @format */

import { AppError, ValidationError } from '@/lib/errors';
import { ErrorCodes } from '@/lib/error-codes';

export function toHttpError(
  e: unknown,
  correlationId?: string,
): {
  status: number;
  body: { code: string; message: string; requestId?: string };
} {
  const baseBody = {
    ...(correlationId && { requestId: correlationId }),
  };

  if (e instanceof ValidationError) {
    return {
      status: e.status,
      body: {
        code: e.code,
        message: e.message,
        ...baseBody,
      },
    };
  }
  if (e instanceof AppError) {
    return {
      status: e.status,
      body: {
        code: e.code,
        message: e.message,
        ...baseBody,
      },
    };
  }
  return {
    status: 500,
    body: {
      code: ErrorCodes.INTERNAL,
      message: 'Something went wrong',
      ...baseBody,
    },
  };
}
