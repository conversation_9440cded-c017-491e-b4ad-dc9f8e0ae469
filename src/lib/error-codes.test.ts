/** @format */

import { ErrorCodes, type ErrorCode } from '@/lib/error-codes';

describe('ErrorCodes', () => {
  it('exports expected error codes', () => {
    expect(ErrorCodes.REPOSITORY).toBe('REPOSITORY');
    expect(ErrorCodes.VALIDATION).toBe('VALIDATION');
    expect(ErrorCodes.AUTH).toBe('AUTH');
    expect(ErrorCodes.NOT_FOUND).toBe('NOT_FOUND');
    expect(ErrorCodes.CONFLICT).toBe('CONFLICT');
    expect(ErrorCodes.BOOTSTRAP_FAILED).toBe('BOOTSTRAP_FAILED');
    expect(ErrorCodes.INTERNAL).toBe('INTERNAL');
  });

  it('has correct TypeScript typing', () => {
    const code: ErrorCode = ErrorCodes.VALIDATION;
    expect(typeof code).toBe('string');
    expect(code).toBe('VALIDATION');
  });

  it('contains all expected keys', () => {
    const expectedKeys = [
      'REPOSITORY',
      'VALIDATION',
      'AUTH',
      'NOT_FOUND',
      'CONFLICT',
      'BOOTSTRAP_FAILED',
      'INTERNAL',
    ];

    expect(Object.keys(ErrorCodes).sort()).toEqual(expectedKeys.sort());
  });
});
