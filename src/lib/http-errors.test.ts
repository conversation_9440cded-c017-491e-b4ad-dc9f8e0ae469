/** @format */

import { toHttpError } from '@/lib/http-errors';
import {
  ValidationError,
  AuthError,
  RepositoryError,
  BootstrapError,
} from '@/lib/errors';
import { ErrorCodes } from '@/lib/error-codes';

describe('toHttpError', () => {
  it('handles ValidationError correctly', () => {
    const error = new ValidationError('Invalid input', { field: 'title' });
    const result = toHttpError(error, 'req-123');

    expect(result).toEqual({
      status: 400,
      body: {
        code: 'VALIDATION',
        message: 'Invalid input',
        requestId: 'req-123',
      },
    });
  });

  it('handles AuthError correctly', () => {
    const error = new AuthError('Unauthorized');
    const result = toHttpError(error, 'req-456');

    expect(result).toEqual({
      status: 401,
      body: {
        code: 'AUTH',
        message: 'Unauthorized',
        requestId: 'req-456',
      },
    });
  });

  it('handles RepositoryError correctly', () => {
    const error = new RepositoryError('DB connection failed');
    const result = toHttpError(error);

    expect(result).toEqual({
      status: 500,
      body: {
        code: 'REPOSITORY',
        message: 'DB connection failed',
      },
    });
  });

  it('handles BootstrapError correctly', () => {
    const error = new BootstrapError('Bootstrap failed');
    const result = toHttpError(error, 'req-789');

    expect(result).toEqual({
      status: 500,
      body: {
        code: 'BOOTSTRAP_FAILED',
        message: 'Bootstrap failed',
        requestId: 'req-789',
      },
    });
  });

  it('handles unknown errors as INTERNAL', () => {
    const error = new Error('Unknown error');
    const result = toHttpError(error, 'req-999');

    expect(result).toEqual({
      status: 500,
      body: {
        code: 'INTERNAL',
        message: 'Something went wrong',
        requestId: 'req-999',
      },
    });
  });

  it('handles non-Error objects as INTERNAL', () => {
    const result = toHttpError('string error', 'req-111');

    expect(result).toEqual({
      status: 500,
      body: {
        code: 'INTERNAL',
        message: 'Something went wrong',
        requestId: 'req-111',
      },
    });
  });

  it('omits requestId when not provided', () => {
    const error = new ValidationError('Invalid');
    const result = toHttpError(error);

    expect(result).toEqual({
      status: 400,
      body: {
        code: 'VALIDATION',
        message: 'Invalid',
      },
    });
    expect(result.body.requestId).toBeUndefined();
  });
});
