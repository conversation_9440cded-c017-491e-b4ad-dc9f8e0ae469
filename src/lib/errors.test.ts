/** @format */

import {
  AppError,
  RepositoryError,
  ValidationError,
  AuthError,
  NotFoundError,
  ConflictError,
  BootstrapError,
} from '@/lib/errors';
import { ErrorCodes } from '@/lib/error-codes';

describe('Error classes', () => {
  describe('AppError', () => {
    it('creates error with code, status, and message', () => {
      const error = new AppError(ErrorCodes.INTERNAL, 500, 'Test error');
      expect(error.code).toBe('INTERNAL');
      expect(error.status).toBe(500);
      expect(error.message).toBe('Test error');
      expect(error.name).toBe('AppError');
    });

    it('includes cause in error chain', () => {
      const cause = new Error('Original error');
      const error = new AppError(ErrorCodes.INTERNAL, 500, 'Wrapped error', {
        cause,
      });
      expect(error.cause).toBe(cause);
    });

    it('assigns metadata to error object', () => {
      const meta = { userId: 'u1', operation: 'test' };
      const error = new AppError(ErrorCodes.INTERNAL, 500, 'Test error', {
        meta,
      });
      const errWithMeta = error as unknown as Error & {
        userId: string;
        operation: string;
      };
      expect(errWithMeta.userId).toBe('u1');
      expect(errWithMeta.operation).toBe('test');
    });
  });

  describe('RepositoryError', () => {
    it('creates error with REPOSITORY code and 500 status', () => {
      const error = new RepositoryError('DB failed');
      expect(error.code).toBe(ErrorCodes.REPOSITORY);
      expect(error.status).toBe(500);
      expect(error.message).toBe('DB failed');
    });
  });

  describe('ValidationError', () => {
    it('creates error with VALIDATION code and 400 status', () => {
      const error = new ValidationError('Invalid input');
      expect(error.code).toBe(ErrorCodes.VALIDATION);
      expect(error.status).toBe(400);
      expect(error.message).toBe('Invalid input');
    });
  });

  describe('AuthError', () => {
    it('creates error with AUTH code and 401 status', () => {
      const error = new AuthError('Unauthorized');
      expect(error.code).toBe(ErrorCodes.AUTH);
      expect(error.status).toBe(401);
      expect(error.message).toBe('Unauthorized');
    });
  });

  describe('NotFoundError', () => {
    it('creates error with NOT_FOUND code and 404 status', () => {
      const error = new NotFoundError('Resource not found');
      expect(error.code).toBe(ErrorCodes.NOT_FOUND);
      expect(error.status).toBe(404);
      expect(error.message).toBe('Resource not found');
    });
  });

  describe('ConflictError', () => {
    it('creates error with CONFLICT code and 409 status', () => {
      const error = new ConflictError('Resource conflict');
      expect(error.code).toBe(ErrorCodes.CONFLICT);
      expect(error.status).toBe(409);
      expect(error.message).toBe('Resource conflict');
    });
  });

  describe('BootstrapError', () => {
    it('creates error with BOOTSTRAP_FAILED code and 500 status', () => {
      const cause = new Error('Profile failed');
      const error = new BootstrapError('Bootstrap failed', cause, {
        userId: 'u1',
      });
      expect(error.code).toBe(ErrorCodes.BOOTSTRAP_FAILED);
      expect(error.status).toBe(500);
      expect(error.message).toBe('Bootstrap failed');
      expect(error.cause).toBe(cause);
      const errWithMeta = error as unknown as Error & { userId: string };
      expect(errWithMeta.userId).toBe('u1');
    });
  });
});
