import { AsyncLocalStorage } from 'async_hooks';
import { randomBytes, randomUUID } from 'node:crypto';
import type { RequestContextShape } from './types';

const storage = new AsyncLocalStorage<RequestContextShape>();

/**
 * Get the current request context (if any).
 */
export function getRequestContext(): RequestContextShape | undefined {
  return storage.getStore();
}

/**
 * Run a function within the provided request context.
 */
export function runWithRequestContext<T>(
  context: RequestContextShape,
  fn: () => T,
): T {
  return storage.run(context, fn);
}

/**
 * Ensure a correlationId exists in the context and run the provided function.
 */
export function withCorrelationId<T>(correlationId: string, fn: () => T): T {
  const existing = getRequestContext();
  const next: RequestContextShape = { ...(existing || {}), correlationId };
  return runWithRequestContext(next, fn);
}

/**
 * Extract or generate a correlation ID from various header shapes.
 * - Supports Fetch API Headers and Node's IncomingHttpHeaders.
 */
export function getOrCreateCorrelationId(
  headers: Headers | Record<string, unknown> | undefined,
): string {
  const lower = 'x-request-id';
  let fromHeader: string | undefined;

  if (headers && typeof (headers as Headers).get === 'function') {
    fromHeader = (headers as Headers).get(lower) || undefined;
  } else if (headers && typeof headers === 'object') {
    const record = headers as Record<string, unknown>;
    const val =
      record[lower] ?? record['X-Request-Id'] ?? record['x-request-id'];
    if (typeof val === 'string') fromHeader = val;
    if (Array.isArray(val) && val.length > 0 && typeof val[0] === 'string')
      fromHeader = val[0];
  }

  return fromHeader || generateId();
}

/** Generate a safe correlation ID. */
export function generateId(): string {
  try {
    return randomUUID();
  } catch {
    try {
      return randomBytes(16).toString('hex');
    } catch {
      return `${Date.now()}-${Math.random().toString(16).slice(2)}`;
    }
  }
}
