import pino, {
  Logger as PinoLogger, type DestinationStream
} from 'pino';
import { createRequire } from 'node:module';
import type { Logger, LoggerConfig } from './types';
import { resolveLoggerConfig } from './config';
import { getRequestContext } from './context';

type AnyRecord = Record<string, unknown>;

function isError(value: unknown): value is Error {
  return (
    value instanceof Error ||
    (typeof value === 'object' &&
      value !== null &&
      'name' in value &&
      'message' in value)
  );
}

/** Serialize an Error to a plain object including cause if present. */
function serializeError(err: Error): AnyRecord {
  const base: AnyRecord = {
    type: err.name,
    message: err.message,
    stack: err.stack,
  };
  const anyErr = err as unknown as AnyRecord;
  if (anyErr.cause) {
    if (isError(anyErr.cause)) {
      base.cause = serializeError(anyErr.cause as unknown as Error);
    } else {
      base.cause = safeValue(anyErr.cause as unknown);
    }
  }
  // Copy enumerable custom props safely
  for (const key of Object.keys(anyErr)) {
    if (
      key === 'name' ||
      key === 'message' ||
      key === 'stack' ||
      key === 'cause'
    )
      continue;
    const value = anyErr[key];
    if (value === undefined) continue;
    base[key] = safeValue(value);
  }
  return base;
}

/** Safely convert untrusted values to JSON-serializable forms, handling circular refs. */
function safeValue(input: unknown, seen = new WeakSet<object>()): unknown {
  if (input === null || input === undefined) return input;
  if (typeof input !== 'object') return input;
  if (isError(input)) return serializeError(input);
  const obj = input as AnyRecord;
  if (seen.has(obj)) return '[Circular]';
  seen.add(obj);
  const out: AnyRecord = Array.isArray(obj) ? ([] as unknown as AnyRecord) : {};
  for (const [k, v] of Object.entries(obj)) {
    out[k] = safeValue(v as unknown, seen);
  }
  return out;
}

/** Create the underlying Pino instance given configuration. */
function createPino(config: Required<LoggerConfig>): PinoLogger {
  const isEdge = process.env.NEXT_RUNTIME === 'edge';
  const isNode = typeof process !== 'undefined' && !!process.versions?.node;

  // Try to use a pretty stream (not transport) to avoid Next RSC transport resolution issues
  let prettyDestination: DestinationStream | undefined;
  if (config.pretty && isNode && !isEdge) {
    try {
      const req = createRequire(import.meta.url);
      const prettyFactory = req('pino-pretty');
      if (typeof prettyFactory === 'function') {
        prettyDestination = prettyFactory({
          colorize: true,
          translateTime: 'SYS:standard',
          singleLine: false,
          messageFormat: '{msg}',
        });
      }
    } catch {
      console.warn(
        'logger: pretty printing requested but pino-pretty is not available; falling back to JSON',
      );
    }
  }

  const options = {
    name: config.name,
    level: config.level,
    base: config.base,
    redact: config.redact,
    errorKey: 'err',
    serializers: {
      err: (err: unknown) => (isError(err) ? serializeError(err) : err),
    },
  } as const;

  // If we created a pretty stream, pass it as destination; otherwise default destination (stdout JSON)
  return prettyDestination ? pino(options, prettyDestination) : pino(options);
}

/** Wrap a Pino logger to conform to the abstract Logger interface. */
function wrap(pinoLogger: PinoLogger, config: Required<LoggerConfig>): Logger {
  function prepareMeta(meta?: unknown): AnyRecord | undefined {
    if (meta === undefined) return undefined;
    if (meta === null) return undefined;
    if (isError(meta)) return { err: serializeError(meta) };
    if (typeof meta === 'object') return safeValue(meta) as AnyRecord;
    return { meta };
  }

  // function prepareErrorMeta(meta?: unknown): AnyRecord | undefined {
  //   if (meta === undefined || meta === null) return undefined;
  //   if (isError(meta)) return { err: serializeError(meta) };
  //   // Normalize non-Error thrown values
  //   let wrapped: Error;
  //   try {
  //     if (typeof meta === 'string') wrapped = new Error(meta);
  //     else wrapped = new Error(JSON.stringify(safeValue(meta)));
  //   } catch {
  //     wrapped = new Error(String(meta));
  //   }
  //   const out: AnyRecord = { err: serializeError(wrapped) };
  //   // Preserve original value for context without risking cycles
  //   if (typeof meta === 'object') out.thrown = safeValue(meta);
  //   else out.thrown = meta;
  //   return out;
  // }

  function prepareErrorMeta(meta?: unknown): AnyRecord | undefined {
    if (meta === undefined || meta === null) return undefined;
    if (isError(meta)) return { err: serializeError(meta) };

    // NEW: pass through if already normalized with an `err` field
    if (typeof meta === 'object' && meta && 'err' in (meta as AnyRecord)) {
      // Ensure values are serializable without re-wrapping
      return safeValue(meta) as AnyRecord;
    }

    // Normalize non-Error thrown values only
    let wrapped: Error;
    try {
      if (typeof meta === 'string') wrapped = new Error(meta);
      else wrapped = new Error(JSON.stringify(safeValue(meta)));
    } catch {
      wrapped = new Error(String(meta));
    }
    const out: AnyRecord = { err: serializeError(wrapped) };
    // Optional: only include `thrown` when the original wasn’t an Error and is useful
    if (typeof meta === 'object') out.thrown = safeValue(meta);
    else out.thrown = meta;
    return out;
  }

  function withContext(bindings?: AnyRecord): AnyRecord {
    const ctx = config.enableRequestContext ? getRequestContext() : undefined;
    return ctx?.correlationId
      ? { ...bindings, correlationId: ctx.correlationId }
      : bindings || {};
  }

  return {
    debug(message: string, meta?: unknown) {
      const bindings = withContext(prepareMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.debug(bindings, message);
      else pinoLogger.debug(message);
    },
    info(message: string, meta?: unknown) {
      const bindings = withContext(prepareMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.info(bindings, message);
      else pinoLogger.info(message);
    },
    warn(message: string, meta?: unknown) {
      const bindings = withContext(prepareMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.warn(bindings, message);
      else pinoLogger.warn(message);
    },
    error(message: string, meta?: unknown) {
      const bindings = withContext(prepareErrorMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.error(bindings, message);
      else pinoLogger.error(message);
    },
    child(childBindings: AnyRecord): Logger {
      const child = pinoLogger.child(childBindings);
      return wrap(child, config);
    },
  };
}

let singleton: Logger | undefined;
let underlying: PinoLogger | undefined;
let activeConfig: Required<LoggerConfig> | undefined;

/**
 * Initialize or reconfigure the global logger singleton.
 */
export function configureLogger(config?: LoggerConfig): Logger {
  activeConfig = resolveLoggerConfig(config);
  underlying = createPino(activeConfig);
  singleton = wrap(underlying, activeConfig);
  return singleton;
}

/** Get the global logger singleton, creating it if needed. */
export function getLogger(): Logger {
  if (!singleton) {
    configureLogger();
  }
  return singleton as Logger;
}
