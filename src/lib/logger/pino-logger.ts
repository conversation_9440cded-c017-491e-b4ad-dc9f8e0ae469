import pino, { Logger as <PERSON><PERSON><PERSON><PERSON>ger, TransportSingleOptions } from 'pino';
import type { Logger, LoggerConfig } from './types';
import { resolveLoggerConfig } from './config';
import { getRequestContext } from './context';

type AnyRecord = Record<string, unknown>;

function isError(value: unknown): value is Error {
  return (
    value instanceof Error ||
    (typeof value === 'object' &&
      value !== null &&
      'name' in value &&
      'message' in value)
  );
}

/** Serialize an Error to a plain object including cause if present. */
function serializeError(err: Error): AnyRecord {
  const base: AnyRecord = {
    type: err.name,
    message: err.message,
    stack: err.stack,
  };
  const anyErr = err as unknown as AnyRecord;
  if (anyErr.cause && isError(anyErr.cause)) {
    base.cause = serializeError(anyErr.cause as unknown as Error);
  }
  // Copy enumerable custom props safely
  for (const key of Object.keys(anyErr)) {
    if (
      key === 'name' ||
      key === 'message' ||
      key === 'stack' ||
      key === 'cause'
    )
      continue;
    const value = anyErr[key];
    if (value === undefined) continue;
    base[key] = safeValue(value);
  }
  return base;
}

/** Safely convert untrusted values to JSON-serializable forms, handling circular refs. */
function safeValue(input: unknown, seen = new WeakSet<object>()): unknown {
  if (input === null || input === undefined) return input;
  if (typeof input !== 'object') return input;
  if (isError(input)) return serializeError(input);
  const obj = input as AnyRecord;
  if (seen.has(obj)) return '[Circular]';
  seen.add(obj);
  const out: AnyRecord = Array.isArray(obj) ? ([] as unknown as AnyRecord) : {};
  for (const [k, v] of Object.entries(obj)) {
    out[k] = safeValue(v as unknown, seen);
  }
  return out;
}

/** Create the underlying Pino instance given configuration. */
function createPino(config: Required<LoggerConfig>): PinoLogger {
  let transport: TransportSingleOptions | undefined;
  if (config.pretty) {
    transport = {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'SYS:standard',
        singleLine: false,
        messageFormat: '{msg}',
      },
    };
  }

  return pino({
    name: config.name,
    level: config.level,
    base: config.base,
    redact: config.redact,
    transport,
    serializers: {
      err: (err: unknown) => (isError(err) ? serializeError(err) : err),
    },
  });
}

/** Wrap a Pino logger to conform to the abstract Logger interface. */
function wrap(pinoLogger: PinoLogger, config: Required<LoggerConfig>): Logger {
  function prepareMeta(meta?: unknown): AnyRecord | undefined {
    if (meta === undefined) return undefined;
    if (meta === null) return undefined;
    if (isError(meta)) return { err: serializeError(meta) };
    if (typeof meta === 'object') return safeValue(meta) as AnyRecord;
    return { meta };
  }

  function prepareErrorMeta(meta?: unknown): AnyRecord | undefined {
    if (meta === undefined || meta === null) return undefined;
    if (isError(meta)) return { err: serializeError(meta) };
    // Normalize non-Error thrown values
    let wrapped: Error;
    try {
      if (typeof meta === 'string') wrapped = new Error(meta);
      else wrapped = new Error(JSON.stringify(safeValue(meta)));
    } catch {
      wrapped = new Error(String(meta));
    }
    const out: AnyRecord = { err: serializeError(wrapped) };
    // Preserve original value for context without risking cycles
    if (typeof meta === 'object') out.thrown = safeValue(meta);
    else out.thrown = meta;
    return out;
  }

  function withContext(bindings?: AnyRecord): AnyRecord {
    const ctx = config.enableRequestContext ? getRequestContext() : undefined;
    return ctx?.correlationId
      ? { ...bindings, correlationId: ctx.correlationId }
      : bindings || {};
  }

  return {
    debug(message: string, meta?: unknown) {
      const bindings = withContext(prepareMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.debug(bindings, message);
      else pinoLogger.debug(message);
    },
    info(message: string, meta?: unknown) {
      const bindings = withContext(prepareMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.info(bindings, message);
      else pinoLogger.info(message);
    },
    warn(message: string, meta?: unknown) {
      const bindings = withContext(prepareMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.warn(bindings, message);
      else pinoLogger.warn(message);
    },
    error(message: string, meta?: unknown) {
      const bindings = withContext(prepareErrorMeta(meta));
      if (Object.keys(bindings).length > 0) pinoLogger.error(bindings, message);
      else pinoLogger.error(message);
    },
    child(childBindings: AnyRecord): Logger {
      const child = pinoLogger.child(childBindings);
      return wrap(child, config);
    },
  };
}

let singleton: Logger | undefined;
let underlying: PinoLogger | undefined;
let activeConfig: Required<LoggerConfig> | undefined;

/**
 * Initialize or reconfigure the global logger singleton.
 */
export function configureLogger(config?: LoggerConfig): Logger {
  activeConfig = resolveLoggerConfig(config);
  underlying = createPino(activeConfig);
  singleton = wrap(underlying, activeConfig);
  return singleton;
}

/** Get the global logger singleton, creating it if needed. */
export function getLogger(): Logger {
  if (!singleton) {
    configureLogger();
  }
  return singleton as Logger;
}
