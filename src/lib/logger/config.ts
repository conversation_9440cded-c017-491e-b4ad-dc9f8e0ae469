import type { LoggerConfig, LogLevel } from './types';

/** Resolve a boolean-like env var ("true"/"1"). */
function envFlag(value: string | undefined, defaultValue: boolean): boolean {
  if (value === undefined) return defaultValue;
  const normalized = value.trim().toLowerCase();
  return (
    normalized === '1' ||
    normalized === 'true' ||
    normalized === 'yes' ||
    normalized === 'y'
  );
}

/** Resolve the runtime environment. */
function getNodeEnv(): string {
  return process.env.NODE_ENV || 'development';
}

/** Resolve default log level, falling back to 'info'. */
function getDefaultLevel(): LogLevel {
  const level = (
    process.env.LOG_LEVEL ||
    process.env.PINO_LOG_LEVEL ||
    'info'
  ).toLowerCase() as LogLevel;
  const allowed: LogLevel[] = ['debug', 'info', 'warn', 'error'];
  return allowed.includes(level) ? level : 'info';
}

/** Build default logger configuration from environment variables. */
export function resolveLoggerConfig(
  partial?: LoggerConfig,
): Required<LoggerConfig> {
  const env = getNodeEnv();
  const prettyDefault = env !== 'production';
  return {
    level: partial?.level ?? getDefaultLevel(),
    name: partial?.name ?? process.env.APP_NAME ?? 'app',
    base: partial?.base ?? {},
    pretty: partial?.pretty ?? envFlag(process.env.LOG_PRETTY, prettyDefault),
    enableRequestContext: partial?.enableRequestContext ?? true,
    redact: partial?.redact ?? [
      'password',
      'authorization',
      'headers.authorization',
    ],
  };
}
