export type {
  Logger,
  LoggerConfig,
  Log<PERSON><PERSON>l,
  RequestContextShape,
} from './types';
export { resolveLoggerConfig } from './config';

import type { Logger, LoggerConfig, LogLevel } from './types';

// -------- Environment detection --------
function isServerRuntime(): boolean {
  return typeof window === 'undefined';
}

// -------- Browser-safe helpers (no Node imports) --------
type AnyRecord = Record<string, unknown>;

function isError(value: unknown): value is Error {
  return (
    value instanceof Error ||
    (typeof value === 'object' &&
      value !== null &&
      'name' in value &&
      'message' in value)
  );
}

function serializeError(err: Error): AnyRecord {
  const base: AnyRecord = {
    type: err.name,
    message: err.message,
    stack: err.stack,
  };
  const anyErr = err as unknown as AnyRecord;
  if (anyErr.cause) {
    if (isError(anyErr.cause as unknown)) {
      base.cause = serializeError(anyErr.cause as unknown as Error);
    } else {
      base.cause = safeValue(anyErr.cause as unknown);
    }
  }
  for (const key of Object.keys(anyErr)) {
    if (
      key === 'name' ||
      key === 'message' ||
      key === 'stack' ||
      key === 'cause'
    )
      continue;
    const value = (anyErr as AnyRecord)[key];
    if (value === undefined) continue;
    base[key] = safeValue(value);
  }
  return base;
}

function safeValue(input: unknown, seen = new WeakSet<object>()): unknown {
  if (input === null || input === undefined) return input;
  if (typeof input !== 'object') return input;
  if (isError(input)) return serializeError(input);
  const obj = input as AnyRecord;
  if (seen.has(obj)) return '[Circular]';
  seen.add(obj);
  const out: AnyRecord = Array.isArray(obj) ? ([] as unknown as AnyRecord) : {};
  for (const [k, v] of Object.entries(obj)) {
    out[k] = safeValue(v as unknown, seen);
  }
  return out;
}

// -------- Request context helpers (no-ops in browser) --------
export function getRequestContext(): { correlationId?: string } | undefined {
  // Universal helper: returns undefined in browser; server logger injects context internally.
  return undefined;
}

export function runWithRequestContext<T>(
  _context: { correlationId?: string },
  fn: () => T,
): T {
  // No-op wrapper in universal build; server-side pino wrapper reads context directly.
  return fn();
}

export function withCorrelationId<T>(correlationId: string, fn: () => T): T {
  // No-op in universal build; recommended use is to call this within server handlers
  // where server-side implementation will bind context via pino wrapper.
  return fn();
}

export function getOrCreateCorrelationId(
  headers: Headers | Record<string, unknown> | undefined,
): string {
  const lower = 'x-request-id';
  let fromHeader: string | undefined;

  if (headers && typeof (headers as Headers).get === 'function') {
    fromHeader = (headers as Headers).get(lower) || undefined;
  } else if (headers && typeof headers === 'object') {
    const record = headers as Record<string, unknown>;
    const val =
      record[lower] ??
      (record as AnyRecord)['X-Request-Id'] ??
      (record as AnyRecord)['x-request-id'];
    if (typeof val === 'string') fromHeader = val;
    if (Array.isArray(val) && val.length > 0 && typeof val[0] === 'string')
      fromHeader = val[0];
  }

  return fromHeader || generateId();
}

function generateId(): string {
  try {
    // Browser and modern runtimes
    if (
      typeof crypto !== 'undefined' &&
      typeof (crypto as unknown as { randomUUID?: () => string }).randomUUID ===
        'function'
    )
      return (crypto as unknown as { randomUUID: () => string }).randomUUID();
  } catch {
    // ignore
  }
  try {
    // Browser getRandomValues fallback
    if (typeof crypto !== 'undefined' && 'getRandomValues' in crypto) {
      const bytes = new Uint8Array(16);
      (
        crypto as unknown as {
          getRandomValues: (arr: Uint8Array) => Uint8Array;
        }
      ).getRandomValues(bytes);
      return Array.from(bytes)
        .map((b) => b.toString(16).padStart(2, '0'))
        .join('');
    }
  } catch {
    // ignore
  }
  return `${Date.now()}-${Math.random().toString(16).slice(2)}`;
}

// -------- Lazy server logger loading with browser fallback --------
type ServerModule = {
  configureLogger: (config?: LoggerConfig) => Logger;
  getLogger: () => Logger;
};
let serverAPI: ServerModule | undefined;
let serverLoading = false;
let browserSingleton: Logger | undefined;
let lastConfig: LoggerConfig | undefined;

// Buffer server log calls until server logger is ready to avoid race/early console fallback
type BufferedCall = {
  level: LogLevel;
  message: string;
  meta?: Record<string, unknown>;
};
const pendingCalls: BufferedCall[] = [];
const PENDING_MAX = 500;

function enqueuePending(call: BufferedCall): void {
  if (pendingCalls.length >= PENDING_MAX) {
    pendingCalls.shift();
  }
  pendingCalls.push(call);
}

function drainPending(): void {
  if (!serverAPI) return;
  const toDrain = pendingCalls.splice(0);
  const active = serverAPI.getLogger();
  for (const evt of toDrain) {
    try {
      // Forward prepared meta as-is; server wrapper will treat it as bindings
      (active[evt.level] as (msg: string, meta?: unknown) => void)(
        evt.message,
        evt.meta,
      );
    } catch (err) {
      // Surface but do not throw during drain
      console.error('logger: failed to drain buffered log', err);
    }
  }
}

function ensureServerLoaded(): void {
  if (!isServerRuntime() || serverAPI || serverLoading) return;
  serverLoading = true;
  // Use a literal dynamic import so the bundler can resolve the chunk in server build
  void import('./pino-logger')
    .then((m) => {
      serverAPI = m as unknown as ServerModule;
      serverLoading = false;
      if (lastConfig) {
        try {
          serverAPI.configureLogger(lastConfig);
        } catch (err) {
          console.error('logger: failed to configure server logger', err);
        }
      }
      drainPending();
    })
    .catch((err) => {
      serverLoading = false;
      console.error('logger: failed to load server logger', err);
    });
}

function createBrowserLogger(_config?: LoggerConfig): Logger {
  const base: AnyRecord = {};
  function prepareMeta(meta?: unknown): AnyRecord | undefined {
    if (meta === undefined || meta === null) return undefined;
    if (isError(meta)) return { err: serializeError(meta) };
    if (typeof meta === 'object') return safeValue(meta) as AnyRecord;
    return { meta };
  }

  function prepareErrorMeta(meta?: unknown): AnyRecord | undefined {
    if (meta === undefined || meta === null) return undefined;
    if (isError(meta)) return { err: serializeError(meta) };
    let wrapped: Error;
    try {
      if (typeof meta === 'string') wrapped = new Error(meta);
      else wrapped = new Error(JSON.stringify(safeValue(meta)));
    } catch {
      wrapped = new Error(String(meta));
    }
    const out: AnyRecord = { err: serializeError(wrapped) };
    if (typeof meta === 'object') out.thrown = safeValue(meta);
    else out.thrown = meta;
    return out;
  }

  function logWith(
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string,
    meta?: unknown,
  ): void {
    const payload = { ...base, ...prepareMeta(meta) };
    if (isServerRuntime() && !serverAPI) {
      const metaBindings =
        Object.keys(payload).length > 0 ? payload : undefined;
      enqueuePending({ level, message, meta: metaBindings });
      return;
    }
    if (Object.keys(payload).length > 0)
      console[level]({ ...payload, level, msg: message });
    else console[level](message);
  }

  return {
    debug(message: string, meta?: unknown) {
      // If server logger is ready, delegate
      if (isServerRuntime() && serverAPI)
        return serverAPI.getLogger().debug(message, meta);
      logWith('debug', message, meta);
    },
    info(message: string, meta?: unknown) {
      if (isServerRuntime() && serverAPI)
        return serverAPI.getLogger().info(message, meta);
      logWith('info', message, meta);
    },
    warn(message: string, meta?: unknown) {
      if (isServerRuntime() && serverAPI)
        return serverAPI.getLogger().warn(message, meta);
      logWith('warn', message, meta);
    },
    error(message: string, meta?: unknown) {
      if (isServerRuntime() && serverAPI)
        return serverAPI.getLogger().error(message, meta);
      // Prepare error meta consistent with server; buffer if server not ready
      const payload = { ...base, ...prepareErrorMeta(meta) };
      if (isServerRuntime() && !serverAPI) {
        const metaBindings =
          Object.keys(payload).length > 0 ? payload : undefined;
        enqueuePending({ level: 'error', message, meta: metaBindings });
        return;
      }
      if (Object.keys(payload).length > 0)
        console.error({ ...payload, level: 'error', msg: message });
      else console.error(message);
    },
    child(childBindings: AnyRecord): Logger {
      // Merge child bindings into base for browser logger
      const childBase = { ...base, ...childBindings };
      const childLogger = createBrowserLogger();
      // Patch base of returned logger by closing over childBase
      return {
        debug(msg: string, meta?: unknown) {
          const payload = { ...childBase, ...prepareMeta(meta) };
          if (isServerRuntime() && serverAPI)
            return serverAPI.getLogger().child(childBindings).debug(msg, meta);
          if (isServerRuntime() && !serverAPI) {
            const metaBindings =
              Object.keys(payload).length > 0 ? payload : undefined;
            enqueuePending({
              level: 'debug',
              message: msg,
              meta: metaBindings,
            });
            return;
          }
          if (Object.keys(payload).length > 0)
            console.debug({ ...payload, level: 'debug', msg: msg });
          else console.debug(msg);
        },
        info(msg: string, meta?: unknown) {
          const payload = { ...childBase, ...prepareMeta(meta) };
          if (isServerRuntime() && serverAPI)
            return serverAPI.getLogger().child(childBindings).info(msg, meta);
          if (isServerRuntime() && !serverAPI) {
            const metaBindings =
              Object.keys(payload).length > 0 ? payload : undefined;
            enqueuePending({ level: 'info', message: msg, meta: metaBindings });
            return;
          }
          if (Object.keys(payload).length > 0)
            console.info({ ...payload, level: 'info', msg: msg });
          else console.info(msg);
        },
        warn(msg: string, meta?: unknown) {
          const payload = { ...childBase, ...prepareMeta(meta) };
          if (isServerRuntime() && serverAPI)
            return serverAPI.getLogger().child(childBindings).warn(msg, meta);
          if (isServerRuntime() && !serverAPI) {
            const metaBindings =
              Object.keys(payload).length > 0 ? payload : undefined;
            enqueuePending({ level: 'warn', message: msg, meta: metaBindings });
            return;
          }
          if (Object.keys(payload).length > 0)
            console.warn({ ...payload, level: 'warn', msg: msg });
          else console.warn(msg);
        },
        error(msg: string, meta?: unknown) {
          const payload = { ...childBase, ...prepareErrorMeta(meta) };
          if (isServerRuntime() && serverAPI)
            return serverAPI.getLogger().child(childBindings).error(msg, meta);
          if (isServerRuntime() && !serverAPI) {
            const metaBindings =
              Object.keys(payload).length > 0 ? payload : undefined;
            enqueuePending({
              level: 'error',
              message: msg,
              meta: metaBindings,
            });
            return;
          }
          if (Object.keys(payload).length > 0)
            console.error({ ...payload, level: 'error', msg: msg });
          else console.error(msg);
        },
        child(grandChildBindings: AnyRecord): Logger {
          // Avoid recursion by creating a fresh child logger with merged bindings
          return createBrowserLogger().child({
            ...childBindings,
            ...grandChildBindings,
          });
        },
      } as Logger;
    },
  } as Logger;
}

export function configureLogger(config?: LoggerConfig): Logger {
  lastConfig = config;
  if (isServerRuntime()) {
    ensureServerLoaded();
    if (serverAPI) {
      try {
        const configured = serverAPI.configureLogger(config);
        drainPending();
        return configured;
      } catch (err) {
        console.error('logger: configureLogger failed on server', err);
      }
    }
  }
  browserSingleton = createBrowserLogger(config);
  return browserSingleton;
}

export function getLogger(): Logger {
  if (isServerRuntime()) {
    ensureServerLoaded();
    if (serverAPI) return serverAPI.getLogger();
  }
  if (!browserSingleton) browserSingleton = createBrowserLogger();
  return browserSingleton;
}

// Export a delegating logger object that resolves the active implementation at call time
export const logger: Logger = {
  debug(message: string, meta?: unknown) {
    return getLogger().debug(message, meta);
  },
  info(message: string, meta?: unknown) {
    return getLogger().info(message, meta);
  },
  warn(message: string, meta?: unknown) {
    return getLogger().warn(message, meta);
  },
  error(message: string, meta?: unknown) {
    return getLogger().error(message, meta);
  },
  child(bindings: Record<string, unknown>) {
    return getLogger().child(bindings);
  },
};
