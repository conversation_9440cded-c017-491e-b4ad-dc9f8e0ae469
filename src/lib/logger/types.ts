/**
 * Logging types and public interfaces for a logging abstraction.
 * These types intentionally avoid tying consumers to a specific
 * underlying logging implementation (e.g., Pino).
 */

/** Supported log levels. */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Shape of contextual request information propagated across async boundaries.
 */
export interface RequestContextShape {
  /** Correlates logs that belong to the same request or trace. */
  correlationId?: string;
  /** Additional contextual properties. */
  [key: string]: unknown;
}

/**
 * Logger configuration options, environment-resolvable.
 */
export interface LoggerConfig {
  /** Minimum level to log. Defaults to 'info'. */
  level?: LogLevel;
  /** Logical service or application name. */
  name?: string;
  /** Base fields to include with every log line. */
  base?: Record<string, unknown>;
  /** Pretty-print logs in development. Defaults based on NODE_ENV. */
  pretty?: boolean;
  /**
   * When true, the logger will automatically include the current
   * request's correlationId (if available) in each log call.
   * Enabled by default.
   */
  enableRequestContext?: boolean;
  /** Paths to redact from logs (e.g., ['password', 'headers.authorization']). */
  redact?: string[];
}

/** Public logger interface exposed to application code. */
export interface Logger {
  /** Log a debug message with optional structured metadata. */
  debug: (message: string, meta?: unknown) => void;
  /** Log an informational message with optional structured metadata. */
  info: (message: string, meta?: unknown) => void;
  /** Log a warning with optional structured metadata. */
  warn: (message: string, meta?: unknown) => void;
  /** Log an error or exception with optional structured metadata. */
  error: (message: string, meta?: unknown) => void;
  /** Create a child logger with additional bound fields. */
  child: (bindings: Record<string, unknown>) => Logger;
}
