/** @format */

import { ErrorCodes, type ErrorCode } from '@/lib/error-codes';

export class AppError extends Error {
  constructor(
    public code: ErrorCode,
    public status: number,
    message: string,
    options?: { cause?: unknown; meta?: Record<string, unknown> },
  ) {
    super(message, { cause: options?.cause as unknown as <PERSON>rro<PERSON> });
    this.name = new.target.name;
    Object.assign(this, options?.meta);
  }
}

export class RepositoryError extends AppError {
  constructor(
    message: string,
    cause?: unknown,
    meta?: Record<string, unknown>,
  ) {
    super(ErrorCodes.REPOSITORY, 500, message, { cause, meta });
  }
}

export class ValidationError extends AppError {
  constructor(message: string, meta?: Record<string, unknown>) {
    super(ErrorCodes.VALIDATION, 400, message, { meta });
  }
}

export class AuthError extends AppError {
  constructor(message: string, meta?: Record<string, unknown>) {
    super(ErrorCodes.AUTH, 401, message, { meta });
  }
}

export class NotFoundError extends AppError {
  constructor(message: string, meta?: Record<string, unknown>) {
    super(ErrorCodes.NOT_FOUND, 404, message, { meta });
  }
}

export class ConflictError extends AppError {
  constructor(message: string, meta?: Record<string, unknown>) {
    super(ErrorCodes.CONFLICT, 409, message, { meta });
  }
}

export class BootstrapError extends AppError {
  constructor(
    message: string,
    cause?: unknown,
    meta?: Record<string, unknown>,
  ) {
    super(ErrorCodes.BOOTSTRAP_FAILED, 500, message, { cause, meta });
  }
}
