export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '13.0.4';
  };
  public: {
    Tables: {
      ai_documents: {
        Row: {
          content_md: string;
          created_at: string;
          id: string;
          kind: string;
          model: string | null;
          provider: string | null;
          range_end: string | null;
          range_start: string | null;
          source_refs: Json;
          title: string | null;
          tokens_input: number | null;
          tokens_output: number | null;
          user_id: string;
        };
        Insert: {
          content_md: string;
          created_at?: string;
          id?: string;
          kind: string;
          model?: string | null;
          provider?: string | null;
          range_end?: string | null;
          range_start?: string | null;
          source_refs?: Json;
          title?: string | null;
          tokens_input?: number | null;
          tokens_output?: number | null;
          user_id: string;
        };
        Update: {
          content_md?: string;
          created_at?: string;
          id?: string;
          kind?: string;
          model?: string | null;
          provider?: string | null;
          range_end?: string | null;
          range_start?: string | null;
          source_refs?: Json;
          title?: string | null;
          tokens_input?: number | null;
          tokens_output?: number | null;
          user_id?: string;
        };
        Relationships: [];
      };
      day_reviews: {
        Row: {
          ai_summary: string | null;
          could_better: Json;
          created_at: string;
          date: string;
          did_well: Json;
          id: string;
          ideas_for_tomorrow: Json;
          summary: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          ai_summary?: string | null;
          could_better?: Json;
          created_at?: string;
          date: string;
          did_well?: Json;
          id?: string;
          ideas_for_tomorrow?: Json;
          summary?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          ai_summary?: string | null;
          could_better?: Json;
          created_at?: string;
          date?: string;
          did_well?: Json;
          id?: string;
          ideas_for_tomorrow?: Json;
          summary?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      events: {
        Row: {
          id: string;
          name: string;
          props: Json;
          ts: string;
          user_id: string;
        };
        Insert: {
          id?: string;
          name: string;
          props?: Json;
          ts?: string;
          user_id: string;
        };
        Update: {
          id?: string;
          name?: string;
          props?: Json;
          ts?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      focus_sessions: {
        Row: {
          created_at: string;
          duration_sec: number | null;
          end_at: string | null;
          id: string;
          interruptions: number;
          note: string | null;
          start_at: string;
          task_id: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          duration_sec?: number | null;
          end_at?: string | null;
          id?: string;
          interruptions?: number;
          note?: string | null;
          start_at?: string;
          task_id: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          duration_sec?: number | null;
          end_at?: string | null;
          id?: string;
          interruptions?: number;
          note?: string | null;
          start_at?: string;
          task_id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'focus_sessions_task_id_fkey';
            columns: ['task_id'];
            isOneToOne: false;
            referencedRelation: 'tasks';
            referencedColumns: ['id'];
          },
        ];
      };
      goals: {
        Row: {
          created_at: string;
          exclude_from_ai: boolean;
          id: string;
          order: number;
          status: string;
          timeframe: string | null;
          title: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          exclude_from_ai?: boolean;
          id?: string;
          order?: number;
          status?: string;
          timeframe?: string | null;
          title: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          exclude_from_ai?: boolean;
          id?: string;
          order?: number;
          status?: string;
          timeframe?: string | null;
          title?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      insight_reports: {
        Row: {
          content_json: Json | null;
          content_md: string;
          created_at: string;
          id: string;
          insights_request_id: string;
          source_refs: Json;
          title: string;
          user_id: string;
          version: number;
        };
        Insert: {
          content_json?: Json | null;
          content_md: string;
          created_at?: string;
          id?: string;
          insights_request_id: string;
          source_refs?: Json;
          title: string;
          user_id: string;
          version?: number;
        };
        Update: {
          content_json?: Json | null;
          content_md?: string;
          created_at?: string;
          id?: string;
          insights_request_id?: string;
          source_refs?: Json;
          title?: string;
          user_id?: string;
          version?: number;
        };
        Relationships: [
          {
            foreignKeyName: 'insight_reports_insights_request_id_fkey';
            columns: ['insights_request_id'];
            isOneToOne: false;
            referencedRelation: 'insights_requests';
            referencedColumns: ['id'];
          },
        ];
      };
      insights_requests: {
        Row: {
          attempts: number;
          cache_key: string;
          completed_at: string | null;
          cost_usd: number;
          created_at: string;
          duration_ms: number | null;
          facts_hash: string | null;
          id: string;
          last_error: string | null;
          model: string | null;
          model_version: string | null;
          next_attempt_at: string | null;
          params: Json;
          period_end: string | null;
          period_start: string | null;
          prompt_hash: string | null;
          provider: string | null;
          started_at: string | null;
          status: string;
          tokens_input: number | null;
          tokens_output: number | null;
          type: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          attempts?: number;
          cache_key: string;
          completed_at?: string | null;
          cost_usd?: number;
          created_at?: string;
          duration_ms?: number | null;
          facts_hash?: string | null;
          id?: string;
          last_error?: string | null;
          model?: string | null;
          model_version?: string | null;
          next_attempt_at?: string | null;
          params?: Json;
          period_end?: string | null;
          period_start?: string | null;
          prompt_hash?: string | null;
          provider?: string | null;
          started_at?: string | null;
          status?: string;
          tokens_input?: number | null;
          tokens_output?: number | null;
          type: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          attempts?: number;
          cache_key?: string;
          completed_at?: string | null;
          cost_usd?: number;
          created_at?: string;
          duration_ms?: number | null;
          facts_hash?: string | null;
          id?: string;
          last_error?: string | null;
          model?: string | null;
          model_version?: string | null;
          next_attempt_at?: string | null;
          params?: Json;
          period_end?: string | null;
          period_start?: string | null;
          prompt_hash?: string | null;
          provider?: string | null;
          started_at?: string | null;
          status?: string;
          tokens_input?: number | null;
          tokens_output?: number | null;
          type?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      integration_accounts: {
        Row: {
          access_token: string;
          created_at: string;
          expires_at: string | null;
          id: string;
          provider: string;
          refresh_token: string | null;
          scopes: string[] | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          access_token: string;
          created_at?: string;
          expires_at?: string | null;
          id?: string;
          provider: string;
          refresh_token?: string | null;
          scopes?: string[] | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          access_token?: string;
          created_at?: string;
          expires_at?: string | null;
          id?: string;
          provider?: string;
          refresh_token?: string | null;
          scopes?: string[] | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      log_entries: {
        Row: {
          id: string;
          kind: Database['public']['Enums']['log_kind'];
          sensitive: boolean;
          task_id: string | null;
          text: string | null;
          timestamp: string;
          user_id: string;
        };
        Insert: {
          id?: string;
          kind: Database['public']['Enums']['log_kind'];
          sensitive?: boolean;
          task_id?: string | null;
          text?: string | null;
          timestamp?: string;
          user_id: string;
        };
        Update: {
          id?: string;
          kind?: Database['public']['Enums']['log_kind'];
          sensitive?: boolean;
          task_id?: string | null;
          text?: string | null;
          timestamp?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'log_entries_task_id_fkey';
            columns: ['task_id'];
            isOneToOne: false;
            referencedRelation: 'tasks';
            referencedColumns: ['id'];
          },
        ];
      };
      profiles: {
        Row: {
          created_at: string;
          id: string;
          tz: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          tz?: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          tz?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      push_subscriptions: {
        Row: {
          auth: string;
          created_at: string;
          endpoint: string;
          id: string;
          p256dh: string;
          user_agent: string | null;
          user_id: string;
        };
        Insert: {
          auth: string;
          created_at?: string;
          endpoint: string;
          id?: string;
          p256dh: string;
          user_agent?: string | null;
          user_id: string;
        };
        Update: {
          auth?: string;
          created_at?: string;
          endpoint?: string;
          id?: string;
          p256dh?: string;
          user_agent?: string | null;
          user_id?: string;
        };
        Relationships: [];
      };
      settings: {
        Row: {
          ai_on: boolean;
          allow_external_ai: boolean;
          created_at: string;
          evening_time: string;
          id: string;
          morning_time: string;
          notifications_on: boolean;
          timer_default_min: number;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          ai_on?: boolean;
          allow_external_ai?: boolean;
          created_at?: string;
          evening_time?: string;
          id?: string;
          morning_time?: string;
          notifications_on?: boolean;
          timer_default_min?: number;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          ai_on?: boolean;
          allow_external_ai?: boolean;
          created_at?: string;
          evening_time?: string;
          id?: string;
          morning_time?: string;
          notifications_on?: boolean;
          timer_default_min?: number;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      tasks: {
        Row: {
          completed_at: string | null;
          created_at: string;
          external_id: string | null;
          external_source: string | null;
          first_planned_date: string | null;
          goal_id: string | null;
          id: string;
          link: string | null;
          order: number;
          planned_for_date: string | null;
          rollover_count: number;
          started_at: string | null;
          status: Database['public']['Enums']['task_status'];
          title: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          completed_at?: string | null;
          created_at?: string;
          external_id?: string | null;
          external_source?: string | null;
          first_planned_date?: string | null;
          goal_id?: string | null;
          id?: string;
          link?: string | null;
          order?: number;
          planned_for_date?: string | null;
          rollover_count?: number;
          started_at?: string | null;
          status?: Database['public']['Enums']['task_status'];
          title: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          completed_at?: string | null;
          created_at?: string;
          external_id?: string | null;
          external_source?: string | null;
          first_planned_date?: string | null;
          goal_id?: string | null;
          id?: string;
          link?: string | null;
          order?: number;
          planned_for_date?: string | null;
          rollover_count?: number;
          started_at?: string | null;
          status?: Database['public']['Enums']['task_status'];
          title?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'tasks_goal_id_fkey';
            columns: ['goal_id'];
            isOneToOne: false;
            referencedRelation: 'goals';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      log_kind:
        | 'session_start'
        | 'session_end'
        | 'note'
        | 'task_done'
        | 'task_carried';
      task_status: 'todo' | 'doing' | 'done' | 'archived';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<
  keyof Database,
  'public'
>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      log_kind: [
        'session_start',
        'session_end',
        'note',
        'task_done',
        'task_carried',
      ],
      task_status: ['todo', 'doing', 'done', 'archived'],
    },
  },
} as const;
