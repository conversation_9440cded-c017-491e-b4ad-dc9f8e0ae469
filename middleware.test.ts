/** @format */

import { NextRequest } from 'next/server';
import { middleware } from './middleware';

jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(),
    },
  })),
}));

describe('middleware auth', () => {
  const { createServerClient } = jest.requireMock('@supabase/ssr');

  function buildRequest(pathname: string) {
    const url = `http://localhost${pathname}`;
    return new NextRequest(new Request(url));
  }

  it('redirects unauthenticated users to /login on protected routes', async () => {
    createServerClient.mockReturnValueOnce({
      auth: { getUser: jest.fn().mockResolvedValue({ data: { user: null } }) },
    });

    const req = buildRequest('/app');
    const res = await middleware(req);
    expect(res.status).toBe(307);
    expect(res.headers.get('location')).toBe('http://localhost/login');
  });

  it('allows public routes without redirect', async () => {
    createServerClient.mockReturnValueOnce({
      auth: { getUser: jest.fn().mockResolvedValue({ data: { user: null } }) },
    });

    const req = buildRequest('/login');
    const res = await middleware(req);
    expect(res.status).toBe(200);
  });

  it('allows authenticated users through', async () => {
    createServerClient.mockReturnValueOnce({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: { id: 'u1' } } }),
      },
    });

    const req = buildRequest('/');
    const res = await middleware(req);
    expect(res.status).toBe(200);
  });
});
