/** @format */

import nextJest from 'next/jest.js';

const createJestConfig = nextJest({
  dir: './',
});

const config = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testMatch: ['**/*.test.[jt]s?(x)'],
  transform: {},
  collectCoverage: true,
  collectCoverageFrom: [
    'middleware.ts',
    'src/app/auth/**/*.{ts,tsx}',
    'src/lib/supabase/auth-helpers.ts',
  ],
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: ['text-summary'],
  // coverageThreshold: {
  //   global: {
  //     statements: 85,
  //     branches: 75,
  //     functions: 60,
  //     lines: 85,
  //   },
  // },
} satisfies import('jest').Config;

export default createJestConfig(config);
