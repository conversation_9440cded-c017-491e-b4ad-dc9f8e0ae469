## Momentum Pilot

A Next.js 15 + TypeScript app with Tailwind CSS and a curated set of accessible UI primitives (Radix + shadcn/ui style components). This README restores the project documentation that was accidentally removed.

### Features

- **Next.js App Router** (`src/app`)
- **TypeScript** with strict tooling
- **Tailwind CSS** with `tailwind-merge` and `tailwindcss-animate`
- **Radix UI** primitives with custom components under `src/components/ui`
- **TanStack Query** for data fetching
- **Form** helpers via `react-hook-form` and `zod`
- **ESLint + Prettier** configured for Next.js and TypeScript
- **Husky + lint-staged** pre-commit hooks (includes Markdown formatting)

### Tech stack

- Next.js 15, React 18
- TypeScript 5
- Tailwind CSS 3
- Radix UI, shadcn-style UI components
- TanStack Query, Zod, date-fns, recharts

---

## Getting started

### Prerequisites

- Node.js 18.18+ (Recommended: latest LTS) and Yarn
  - Check: `node -v` and `yarn -v`
  - If you use `nvm`: `nvm use` (make sure a compatible version is installed)

### Install

```bash
yarn install
```

### Development

```bash
yarn dev
```

Starts the Next.js dev server at `http://localhost:3000`.

### Linting and type checks

```bash
yarn lint       # ESLint (Next.js + TS config)
yarn validate   # TypeScript noEmit + ESLint
```

### Formatting

```bash
yarn format         # Prettier write
yarn format:check   # Prettier check
```

### Production build

```bash
yarn build  # Next.js production build
yarn start  # Run the built app
```

Scripts are defined in `package.json`:

- `dev`: Next dev server
- `build`: Next build
- `start`: Next start
- `lint`: Next lint
- `validate`: `tsc --noEmit && yarn lint`
- `format`, `format:check`: Prettier
- `prepare`: Husky installation hook

---

## Project structure

```
src/
  app/               # App Router pages, layouts, and global styles
    globals.css
    layout.tsx
    page.tsx
  components/
    ui/              # Reusable UI primitives (Radix + shadcn-style)
    compass/         # Domain components (NorthStar, TheLog, TodaysPath)
    phases/          # Feature flows (MorningKickoff, EveningReview)
    timer/           # FocusTimer and related
  hooks/             # Reusable React hooks
  lib/               # Utilities and helpers
```

Tailwind is configured via `tailwind.config.ts`. Global styles live in `src/app/globals.css`.

---

## Backend architecture

- See `docs/architecture.md` for how we split logic across `data/`, `services/`, and `routes/`.

---

## UI components

- Components are colocated under `src/components/ui` and are built on Radix primitives for accessibility.
- Use classes and variants via `class-variance-authority` and `tailwind-merge` for predictable styling.

---

## Quality and conventions

### ESLint

- Flat config at `eslint.config.js` includes Next.js rules and TypeScript support.
- React Fast Refresh rules are relaxed for `src/components/ui/**/*` and `src/app/**/layout.tsx`.

### Prettier

- Enforced for `*.{css,md,json,mdx}` and also after ESLint for `*.{js,jsx,ts,tsx}` via `lint-staged`.

### Husky + lint-staged

- Pre-commit runs ESLint (with `--fix`) and Prettier on staged files.
- Markdown (`*.md`) files are included and will be auto-formatted.

---

## Deployment

### Vercel (recommended)

- This is a standard Next.js app; the default Vercel settings work out-of-the-box.
- Build command: `yarn build`
- Install command: `yarn install`
- Output: Next.js (no custom output directory needed)

### Other platforms

- Ensure Node.js 18.18+.
- Run `yarn build` during CI, then `yarn start` to serve the production build.

---

## Troubleshooting

- Type errors or lints failing: run `yarn validate` locally.
- Node version mismatches: use `nvm` to switch to a compatible version.
- Styling issues: ensure Tailwind classes are not purged unexpectedly and that `globals.css` is imported in `layout.tsx`.

---

## Contributing

1. Create a feature branch from your base branch.
2. Make changes and commit. Husky will run pre-commit hooks.
3. Open a PR. Keep commit messages and PR titles descriptive. Avoid accidental file deletions.

If this README drifts from reality, update it alongside code changes to keep the documentation accurate.
