import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Enable Sentry in prod by default; allow opt-in in development
  enabled:
    process.env.NODE_ENV !== 'development' ||
    process.env.SENTRY_ENABLE_DEV === 'true',

  // Environment and release tagging
  environment: process.env.VERCEL_ENV ?? process.env.NODE_ENV ?? 'development',
  release: process.env.NEXT_PUBLIC_SENTRY_RELEASE,

  // Performance monitoring
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

  // Limit where trace headers are propagated
  tracePropagationTargets: [
    'localhost',
    ...(typeof window !== 'undefined' ? [window.location.origin] : []),
  ],

  // Session replay with privacy defaults
  replaysSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  replaysOnErrorSampleRate: 1.0,
  integrations: [
    Sentry.replayIntegration({
      maskAllText: true,
      blockAllMedia: true,
    }),
  ],

  // Route client events through a tunnel to avoid ad-blockers and hide DSN
  tunnel: '/api/sentry-tunnel',

  // Filter out localhost noise if enabled in dev
  beforeSend(event) {
    if (
      process.env.NODE_ENV === 'development' &&
      process.env.SENTRY_ENABLE_DEV !== 'true'
    ) {
      return null;
    }

    if (
      event.request?.url?.includes('localhost') ||
      event.request?.url?.includes('127.0.0.1')
    ) {
      return null;
    }

    return event;
  },
});

// Required for navigation instrumentation in Next.js App Router
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
