-- ========== Extensions ==========
create extension if not exists pgcrypto;

-- ========== Helpers ==========
-- updated_at trigger
create or replace function public.set_updated_at()
returns trigger language plpgsql as $$
begin
  new.updated_at = now();
  return new;
end $$;

-- ========== Enums ==========
do $$
begin
  if not exists (select 1 from pg_type where typname = 'task_status') then
    create type public.task_status as enum ('todo','doing','done','archived');
  end if;

  if not exists (select 1 from pg_type where typname = 'log_kind') then
    create type public.log_kind as enum ('session_start','session_end','note','task_done');
  end if;

  -- Add task_carried if missing
  if not exists (
    select 1 from pg_type t
    join pg_enum e on e.enumtypid = t.oid
    where t.typname = 'log_kind' and e.enumlabel = 'task_carried'
  ) then
    alter type public.log_kind add value 'task_carried';
  end if;
end $$;

-- ========== Profiles ==========
create table if not exists public.profiles (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null unique references auth.users(id) on delete cascade,
  tz text not null default 'UTC',
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

-- ========== Settings ==========
create table if not exists public.settings (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null unique references auth.users(id) on delete cascade,
  morning_time time not null default '04:00',
  evening_time time not null default '17:00',
  timer_default_min int not null default 25,
  notifications_on boolean not null default true,
  ai_on boolean not null default true,
  allow_external_ai boolean not null default true,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

-- ========== Goals ==========
create table if not exists public.goals (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  title text not null,
  timeframe text,
  "order" int not null default 0,
  status text not null default 'active',
  exclude_from_ai boolean not null default false,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
create index if not exists goals_user_order_idx on public.goals (user_id, "order");

-- ========== Tasks ==========
create table if not exists public.tasks (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  goal_id uuid references public.goals(id) on delete set null,
  title text not null,
  status public.task_status not null default 'todo',
  planned_for_date date,
  first_planned_date date,
  started_at timestamptz,
  completed_at timestamptz,
  rollover_count int not null default 0,
  "order" int not null default 0,
  external_source text,
  external_id text,
  link text,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  constraint tasks_external_unique unique (user_id, external_source, external_id)
);
create index if not exists tasks_today_idx on public.tasks (user_id, planned_for_date, status, "order");
create index if not exists tasks_goal_idx on public.tasks (user_id, goal_id);
create index if not exists tasks_completed_idx on public.tasks (user_id, completed_at);

-- Auto-set completed_at when status transitions to 'done'
create or replace function public.set_task_completed_at()
returns trigger language plpgsql as $$
begin
  if new.status = 'done' and (old.status is distinct from 'done') and new.completed_at is null then
    new.completed_at = now();
  end if;
  return new;
end $$;
drop trigger if exists trg_tasks_completed_at on public.tasks;
create trigger trg_tasks_completed_at
before update on public.tasks
for each row
when (old.status is distinct from new.status or old.completed_at is distinct from new.completed_at)
execute procedure public.set_task_completed_at();

-- Set first_planned_date when task is first planned
create or replace function public.set_task_first_planned_date()
returns trigger language plpgsql as $$
begin
  if (old.planned_for_date is null)
     and (new.planned_for_date is not null)
     and (new.first_planned_date is null) then
    new.first_planned_date = new.planned_for_date;
  end if;
  return new;
end $$;
drop trigger if exists trg_tasks_first_planned on public.tasks;
create trigger trg_tasks_first_planned
before update on public.tasks
for each row
when (old.planned_for_date is distinct from new.planned_for_date)
execute procedure public.set_task_first_planned_date();

-- ========== Focus Sessions ==========
create table if not exists public.focus_sessions (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  task_id uuid not null references public.tasks(id) on delete cascade,
  start_at timestamptz not null default now(),
  end_at timestamptz,
  duration_sec int,
  interruptions int not null default 0,
  note text,
  created_at timestamptz not null default now()
);
-- Single active session per user
create unique index if not exists focus_sessions_active_unique
  on public.focus_sessions (user_id) where end_at is null;
create index if not exists focus_sessions_user_start_idx
  on public.focus_sessions (user_id, start_at desc);

-- Set tasks.started_at on first focus session
create or replace function public.set_task_started_at_from_session()
returns trigger language plpgsql as $$
begin
  update public.tasks
     set started_at = coalesce(started_at, new.start_at)
   where id = new.task_id
     and started_at is null;
  return new;
end $$;
drop trigger if exists trg_task_started_from_session on public.focus_sessions;
create trigger trg_task_started_from_session
after insert on public.focus_sessions
for each row
execute procedure public.set_task_started_at_from_session();

-- ========== Log Entries ==========
create table if not exists public.log_entries (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  timestamp timestamptz not null default now(),
  kind public.log_kind not null,
  text text,
  task_id uuid references public.tasks(id) on delete set null,
  sensitive boolean not null default false
);
create index if not exists log_entries_user_ts_idx on public.log_entries (user_id, timestamp desc);
create index if not exists log_entries_task_idx on public.log_entries (user_id, task_id);
create index if not exists log_entries_kind_task_idx on public.log_entries (user_id, kind, task_id, timestamp desc);

-- ========== Day Reviews ==========
create table if not exists public.day_reviews (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  date date not null,
  summary text,
  did_well jsonb not null default '[]'::jsonb,
  could_better jsonb not null default '[]'::jsonb,
  ideas_for_tomorrow jsonb not null default '[]'::jsonb,
  ai_summary text,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (user_id, date)
);

-- ========== Integrations ==========
create table if not exists public.integration_accounts (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  provider text not null, -- 'todoist'
  access_token text not null,
  refresh_token text,
  scopes text[],
  expires_at timestamptz,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (user_id, provider)
);

-- ========== Web Push ==========
create table if not exists public.push_subscriptions (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  endpoint text not null,
  p256dh text not null,
  auth text not null,
  user_agent text,
  created_at timestamptz not null default now(),
  unique (endpoint)
);
create index if not exists push_subs_user_idx on public.push_subscriptions (user_id, created_at desc);

-- ========== AI Documents (saved outputs) ==========
create table if not exists public.ai_documents (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  kind text not null, -- 'weekly'|'monthly'|'brag'|'linkedin'|'custom'
  range_start date,
  range_end date,
  title text,
  content_md text not null,
  source_refs jsonb not null default '[]'::jsonb,
  provider text,
  model text,
  tokens_input int,
  tokens_output int,
  created_at timestamptz not null default now()
);
create index if not exists ai_documents_user_kind_idx on public.ai_documents (user_id, kind, created_at desc);

-- ========== Insights (Requests + Reports) ==========
create table if not exists public.insights_requests (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  type text not null check (type in ('weekly_review','brag_doc','responsibilities','ad_hoc')),
  period_start date,
  period_end date,
  params jsonb not null default '{}'::jsonb,
  cache_key text not null,
  facts_hash text,
  status text not null default 'queued' check (status in ('queued','processing','ready','failed')),
  provider text,
  model text,
  model_version text,
  prompt_hash text,
  cost_usd numeric(8,4) not null default 0,
  tokens_input int,
  tokens_output int,
  attempts int not null default 0,
  last_error text,
  started_at timestamptz,
  completed_at timestamptz,
  next_attempt_at timestamptz,
  duration_ms int,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  unique (user_id, cache_key)
);
create index if not exists insights_requests_status_idx on public.insights_requests (status, created_at);
create index if not exists insights_requests_user_created_idx on public.insights_requests (user_id, created_at desc);

create table if not exists public.insight_reports (
  id uuid primary key default gen_random_uuid(),
  insights_request_id uuid not null references public.insights_requests(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  version int not null default 1,
  title text not null,
  content_md text not null,
  content_json jsonb,
  source_refs jsonb not null default '[]'::jsonb,
  created_at timestamptz not null default now(),
  unique (insights_request_id, version)
);
create index if not exists insight_reports_user_created_idx on public.insight_reports (user_id, created_at desc);

-- ========== Analytics Events ==========
create table if not exists public.events (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null, -- e.g., 'session_started'
  ts timestamptz not null default now(),
  props jsonb not null default '{}'::jsonb
);
create index if not exists events_user_ts_idx on public.events (user_id, ts desc);
create index if not exists events_name_idx on public.events (name);

-- ========== Updated_at Triggers ==========
drop trigger if exists trg_profiles_updated_at on public.profiles;
create trigger trg_profiles_updated_at before update on public.profiles
for each row execute procedure public.set_updated_at();

drop trigger if exists trg_settings_updated_at on public.settings;
create trigger trg_settings_updated_at before update on public.settings
for each row execute procedure public.set_updated_at();

drop trigger if exists trg_goals_updated_at on public.goals;
create trigger trg_goals_updated_at before update on public.goals
for each row execute procedure public.set_updated_at();

drop trigger if exists trg_tasks_updated_at on public.tasks;
create trigger trg_tasks_updated_at before update on public.tasks
for each row execute procedure public.set_updated_at();

drop trigger if exists trg_day_reviews_updated_at on public.day_reviews;
create trigger trg_day_reviews_updated_at before update on public.day_reviews
for each row execute procedure public.set_updated_at();

drop trigger if exists trg_integration_accounts_updated_at on public.integration_accounts;
create trigger trg_integration_accounts_updated_at before update on public.integration_accounts
for each row execute procedure public.set_updated_at();

drop trigger if exists trg_insights_requests_updated_at on public.insights_requests;
create trigger trg_insights_requests_updated_at before update on public.insights_requests
for each row execute procedure public.set_updated_at();

-- ========== Row Level Security ==========
alter table public.profiles enable row level security;
alter table public.settings enable row level security;
alter table public.goals enable row level security;
alter table public.tasks enable row level security;
alter table public.focus_sessions enable row level security;
alter table public.log_entries enable row level security;
alter table public.day_reviews enable row level security;
alter table public.integration_accounts enable row level security;
alter table public.push_subscriptions enable row level security;
alter table public.ai_documents enable row level security;
alter table public.insights_requests enable row level security;
alter table public.insight_reports enable row level security;
alter table public.events enable row level security;

-- Ownership policies (one policy name reused per table)
create policy "own rows" on public.profiles
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.settings
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.goals
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.tasks
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.focus_sessions
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.log_entries
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.day_reviews
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.integration_accounts
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.push_subscriptions
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.ai_documents
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.insights_requests
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.insight_reports
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());
create policy "own rows" on public.events
  for all using (user_id = auth.uid()) with check (user_id = auth.uid());