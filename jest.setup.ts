/** @format */

process.env.NEXT_PUBLIC_SUPABASE_URL =
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'anon-key';

jest.mock('next/headers', () => ({
  cookies: async () => ({
    getAll: () => [],
    set: () => undefined,
  }),
}));

jest.mock('next/navigation', () => {
  const actual = jest.requireActual('next/navigation');
  return {
    ...actual,
    redirect: (url: string) => {
      const err = new Error(`REDIRECT:${url}`);
      // Attach a flag to allow tests to detect redirect
      // @ts-expect-error custom
      err.__isRedirect = true;
      throw err;
    },
  };
});
