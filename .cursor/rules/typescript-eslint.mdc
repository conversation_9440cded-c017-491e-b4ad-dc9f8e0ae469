---
globs: *.ts,*.tsx
description: TypeScript, ESLint, and code style conventions
---
# TypeScript and Linting

- TS config: [tsconfig.json](mdc:tsconfig.json)
  - Path alias: `@/*` → `src/*`
  - Module resolution: `bundler`, `jsx: preserve`, `target: ES2017`
- ESLint flat config: [eslint.config.js](mdc:eslint.config.js)
  - Next core web vitals rules included
  - React Hooks rules enabled
  - `react-refresh/only-export-components`: warn globally, disabled for `src/components/ui/**/*` and `src/app/**/layout.tsx`
  - `@typescript-eslint/no-unused-vars`: off (don’t re-enable without project-wide fixes)
- Prefer:
  - Interface for props/types; export types co-located with components
  - Named exports for reusable components/utilities; default exports for Next `page.tsx`
  - Stable component signatures with explicit props types
- Add `'use client'` only when necessary (hooks, event handlers, timers, browser APIs).

