---
alwaysApply: true
description: High-level project structure, entry points, and conventions for Momentum Pilot (Next.js 15 App Router, Tailwind, shadcn-style UI)
---
# Momentum Pilot — Project Structure and Conventions

- **Entry points**
  - App Router root layout: [src/app/layout.tsx](mdc:src/app/layout.tsx)
  - Global styles: [src/app/globals.css](mdc:src/app/globals.css)
  - Providers (TanStack Query): [src/app/providers.tsx](mdc:src/app/providers.tsx)
  - Home page: [src/app/page.tsx](mdc:src/app/page.tsx)
- **UI + Design**
  - Tailwind config: [tailwind.config.ts](mdc:tailwind.config.ts)
  - HSL design tokens and CSS variables live in [src/app/globals.css](mdc:src/app/globals.css)
  - Reusable UI primitives: [src/components/ui](mdc:src/components/ui)
  - Utility `cn`: [src/lib/utils.ts](mdc:src/lib/utils.ts)
- **Domain components**
  - Daily compass: [src/components/DailyCompass.tsx](mdc:src/components/DailyCompass.tsx)
  - Compass: [src/components/compass](mdc:src/components/compass)
  - Phases: [src/components/phases](mdc:src/components/phases)
  - Timer: [src/components/timer](mdc:src/components/timer)
- **Tooling**
  - TypeScript config (paths, module resolution): [tsconfig.json](mdc:tsconfig.json)
  - Linting (flat config): [eslint.config.js](mdc:eslint.config.js)
  - Next config: [next.config.js](mdc:next.config.js)
  - Project overview: [README.md](mdc:README.md)

Key conventions:
- Next.js 15 App Router; prefer Server Components by default; add `'use client'` for interactivity/hooks.
- Use Tailwind + HSL tokens; don’t hardcode colors; prefer `hsl(var(--token))`.
- Import via alias `@/*` → `src/*`.

