## Logger

Brief overview of the production-ready logging abstraction used across the app. It wraps <PERSON><PERSON> behind a small interface so the underlying implementation can be swapped without touching application code.

### Key points

- **Abstraction**: App code uses a minimal API: `debug`, `info`, `warn`, `error`, and `child`.
- **Structured logging**: Pass objects; values are safely serialized (handles Errors and circular refs).
- **Correlation IDs**: Async request context via `AsyncLocalStorage` with helpers to propagate an ID.
- **Environment-aware**: Pretty printing in development; JSON in production.
- **TypeScript**: Fully typed, with a singleton export for convenience.
- **Browser-safe + lazy**: The module avoids Node-only imports in the browser and lazily loads the Pino backend only on the server.
- **Runtime reconfiguration**: `configureLogger(...)` updates the active logger without requiring a process restart; the exported `logger` delegates to the current implementation.

### Where it lives

- Implementation: `src/lib/logger/`
  - `types.ts` – public types (`Logger`, `LoggerConfig`, `LogLevel`)
  - `config.ts` – environment-based configuration resolver
  - `context.ts` – request context and correlation ID helpers
  - `pino-logger.ts` – Pino-backed implementation
  - `index.ts` – clean exports + singleton `logger`

### Quick start

```ts
import { logger } from '@/lib/logger';

logger.info('Server started', { port: 3000 });
logger.debug('Detailed info', { feature: 'x' });

try {
  // ...
} catch (err) {
  logger.error('Operation failed', err as Error);
}
```

### API

- `logger.debug(message: string, meta?: unknown)`
- `logger.info(message: string, meta?: unknown)`
- `logger.warn(message: string, meta?: unknown)`
- `logger.error(message: string, meta?: unknown)`
- `logger.child(bindings: Record<string, unknown>)`

Notes:

- You may pass an `Error` as `meta`; it is serialized correctly as `{ err: ... }` under the hood.
- Passing any object is fine; nested Errors and circular structures are handled safely.
- `error()` normalizes non-Error throws (strings/objects) into a proper `{ err: ... }` and also preserves the original value under `thrown`.

### Request correlation IDs

Use correlation IDs to tie logs to a request or job. The logger automatically injects the current `correlationId` when present.

```ts
// Universal-safe import for ID extraction:
import { getOrCreateCorrelationId } from '@/lib/logger';
// Server-only import for binding the request context:
import { withCorrelationId } from '@/lib/logger/context';

// Example: wrapping a handler
export async function handler(req: Request) {
  const id = getOrCreateCorrelationId(req.headers);
  return withCorrelationId(id, async () => {
    // All logs inside include { correlationId: id }
    // ... handler logic
  });
}
```

Notes:

- `withCorrelationId` is server-only (uses Node `async_hooks` under the hood); do not import it in browser code.
- In universal code paths, it is safe to call `getOrCreateCorrelationId` from `@/lib/logger`.

#### Edge Middleware tip (Next.js)

In Edge Middleware you cannot set Node AsyncLocalStorage. Set an `x-request-id` header there, and then call `withCorrelationId(...)` in your Node route handler to attach context:

```ts
// src/middleware.ts (Edge)
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(req: NextRequest) {
  const headers = new Headers(req.headers);
  if (!headers.get('x-request-id'))
    headers.set('x-request-id', crypto.randomUUID());
  return NextResponse.next({ request: { headers } });
}

export const config = { matcher: ['/api/:path*'] };
```

### Child logger (recommended usage)

Create a module-scoped child logger for persistent context without repeating metadata.

```ts
// src/services/user-service.ts
import { logger } from '@/lib/logger';

const serviceLogger = logger.child({ service: 'UserService' });

export async function getUser(userId: string) {
  serviceLogger.info({ userId }, 'Fetching user data');
  // ...
}
```

All logs from this module include `{ service: 'UserService' }` for easy filtering.

### Configuration

Environment variables (auto-resolved by `resolveLoggerConfig`):

- `LOG_LEVEL` – one of `debug|info|warn|error` (default: `info`)
- `APP_NAME` – service/application name (default: `app`)
- `LOG_PRETTY` – pretty printing (default: `true` in non-production)

Programmatic configuration (optional, can be called at runtime):

```ts
import { configureLogger } from '@/lib/logger';

configureLogger({
  level: 'debug',
  name: 'momentum-pilot',
  base: { env: process.env.NODE_ENV },
  pretty: process.env.NODE_ENV !== 'production',
  redact: ['password', 'headers.authorization'],
});
```

### Pretty vs production

- Non-production: pretty logs via `pino-pretty` (installed) when `LOG_PRETTY=true` or by default outside production.
- Production: structured JSON logs suitable for log aggregation.

### Swapping implementations

Only `pino-logger.ts` depends on Pino. If needed, implement the same `Logger` interface with another library and rewire the exports. Application code remains unchanged.

### Best practices

- Log structured data instead of string concatenation.
- Avoid logging secrets; use `redact` for sensitive paths.
- Include stable keys (e.g., `userId`, `orderId`) for easier querying.

### Client-side logging strategy (optional)

For a unified view of client and server behavior, capture client logs and forward them to an API endpoint that uses the server logger.

```ts
// src/lib/client-logger.ts (browser)
type Level = 'debug' | 'info' | 'warn' | 'error';

const queue: Array<{
  level: Level;
  message: string;
  meta?: unknown;
  t: number;
}> = [];
let flushTimer: number | undefined;

export function clientLog(level: Level, message: string, meta?: unknown) {
  queue.push({ level, message, meta, t: Date.now() });
  scheduleFlush();
}

function scheduleFlush() {
  if (flushTimer) return;
  flushTimer = window.setTimeout(flush, 1000);
}

async function flush() {
  flushTimer = undefined;
  const batch = queue.splice(0, queue.length);
  if (batch.length === 0) return;
  try {
    await fetch('/api/logs', {
      method: 'POST',
      headers: { 'content-type': 'application/json' },
      body: JSON.stringify({ events: batch }),
      keepalive: true,
    });
  } catch {
    // Best-effort; drop on failure or add simple retry if desired
  }
}
```

The exported `logger` is a thin delegate that always uses the current configuration. In the browser it falls back to `console` with the same API; on the server it uses Pino.

```ts
// src/app/api/logs/route.ts (server)
import { NextResponse } from 'next/server';
import {
  logger,
  getOrCreateCorrelationId,
  withCorrelationId,
} from '@/lib/logger';

export async function POST(req: Request) {
  const id = getOrCreateCorrelationId(req.headers);
  return withCorrelationId(id, async () => {
    const { events } = (await req.json()) as {
      events: Array<{
        level: string;
        message: string;
        meta?: unknown;
        t: number;
      }>;
    };
    for (const e of events.slice(0, 200)) {
      const log = logger.child({ source: 'client', t: e.t });
      switch (e.level) {
        case 'debug':
          log.debug(e.message, e.meta);
          break;
        case 'info':
          log.info(e.message, e.meta);
          break;
        case 'warn':
          log.warn(e.message, e.meta);
          break;
        default:
          log.error(e.message, e.meta);
      }
    }
    return NextResponse.json(
      { ok: true },
      { status: 202, headers: { 'x-request-id': id } },
    );
  });
}
```

Notes:

- Consider sampling and redaction for privacy. Use `redact` to avoid logging sensitive data.
- Use `navigator.sendBeacon` for unload scenarios if desired.
