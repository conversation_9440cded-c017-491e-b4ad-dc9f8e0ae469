# Sentry Error Monitoring & Performance Tracking Setup

This project is configured with Sentry for comprehensive error monitoring and performance tracking.

## 🚀 Quick Setup

### 1. Environment Variables

Copy the `.env.example` file to `.env.local`:

```bash
cp .env.example .env.local
```

### 2. Get Your Sentry DSN

1. Sign up at [sentry.io](https://sentry.io)
2. Create a new project (select "Next.js" as the platform)
3. Copy your DSN from the project settings
4. Add it to your `.env.local` file:

```env
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
```

### 3. Optional: Source Map Uploads

For better error tracking in production:

```env
SENTRY_ORG=your-org-name
SENTRY_PROJECT=your-project-name
SENTRY_AUTH_TOKEN=your-auth-token
```

## 🧪 Testing Sentry Integration

### Test Error Reporting

1. **Client-side Error Test:**
   Create a test button that throws an error:

   ```jsx
   <button onClick={() => { throw new Error('Test client error'); }}>
     Test Client Error
   </button>
   ```

2. **Server-side Error Test:**
   Add to a server component or API route:

   ```jsx
   throw new Error('Test server error');
   ```

3. **Unhandled Promise Rejection Test:**

   ```jsx
   <button onClick={() => { 
     Promise.reject(new Error('Test unhandled rejection')); 
   }}>
     Test Promise Rejection
   </button>
   ```

### Test Performance Monitoring

1. **Page Load Tracking:**
   Navigation between pages is automatically tracked

2. **Custom Interactions:**
   Use the performance tracking hooks:

   ```jsx
   import { useSentryInteractionTracking } from '@/hooks/use-sentry-performance';

   function MyComponent() {
     const { trackClick, trackFormSubmission } = useSentryInteractionTracking();

     return (
       <button onClick={() => trackClick('important-button', { context: 'test' })}>
         Track This Click
       </button>
     );
   }
   ```

## 📊 What's Being Monitored

### Automatic Monitoring

- ✅ **Unhandled JavaScript errors**
- ✅ **Unhandled promise rejections**
- ✅ **React component errors** (via Error Boundary)
- ✅ **Page load performance**
- ✅ **API route errors**
- ✅ **Server-side errors**

### Custom Monitoring

- ✅ **User interactions** (clicks, form submissions)
- ✅ **Custom operations** (API calls, data processing)
- ✅ **User context** (when available)

## 🔧 Configuration

### Sample Rates

The current configuration uses different sample rates for development vs production:

- **Development:** 100% error capture, 100% performance monitoring
- **Production:** 10% performance monitoring, 100% error capture

### Environment Detection

Sentry automatically detects the environment based on `NODE_ENV`. You can override this by setting environment variables.

### Development Mode

By default, Sentry is disabled in development to reduce noise. Enable it by setting:

```env
SENTRY_ENABLE_DEV=true
```

## 🎯 Usage Examples

### Setting User Context

```jsx
import { setUserContext } from '@/lib/sentry';

// After user logs in
setUserContext({
  id: user.id,
  email: user.email,
  username: user.username,
});
```

### Tracking Custom Events

```jsx
import { captureEvent, measureAsync } from '@/lib/sentry';

// Track a custom event
captureEvent('User completed onboarding', { step: 5 });

// Measure async operation performance
const result = await measureAsync('data-fetch', async () => {
  return await fetchUserData();
}, { userId: user.id });
```

### Manual Error Capture

```jsx
import * as Sentry from '@sentry/nextjs';

try {
  // risky operation
} catch (error) {
  Sentry.captureException(error, {
    tags: { section: 'user-profile' },
    extra: { userId: user.id },
  });
}
```

## 🚨 Testing in Different Environments

### Development Testing

1. Set `SENTRY_ENABLE_DEV=true` in `.env.local`
2. Restart your dev server: `yarn dev`
3. Trigger test errors (see testing section above)
4. Check your Sentry dashboard for events

### Production Testing

1. Deploy with proper environment variables
2. Test error scenarios in production
3. Verify source maps are working (errors should show original code)

## 🔍 Verifying Setup

### Check Console Logs

In development with Sentry enabled, you should see initialization logs:

```
[Sentry] SDK successfully initialized
```

### Check Network Tab

Look for requests to `sentry.io` when errors occur or performance events are captured.

### Check Sentry Dashboard

1. Go to your Sentry project dashboard
2. Navigate to "Issues" to see captured errors
3. Navigate to "Performance" to see transaction data

## 🛠 Troubleshooting

### Common Issues

1. **No events in Sentry:**
   - Check your DSN is correct
   - Verify environment variables are loaded
   - Check network requests in browser dev tools

2. **Source maps not working:**
   - Verify `SENTRY_ORG`, `SENTRY_PROJECT`, and `SENTRY_AUTH_TOKEN` are set
   - Check build logs for source map upload messages

3. **Too many events:**
   - Adjust sample rates in configuration files
   - Add filters in `beforeSend` functions

### Debug Mode

Add debug logging to Sentry configuration:

```js
Sentry.init({
  // ... other options
  debug: true, // Enable in development only
});
```

## 📚 Additional Resources

- [Sentry Next.js Documentation](https://docs.sentry.io/platforms/javascript/guides/nextjs/)
- [Error Tracking Best Practices](https://docs.sentry.io/product/issues/)
- [Performance Monitoring Guide](https://docs.sentry.io/product/performance/)
- [User Context Documentation](https://docs.sentry.io/enriching-error-data/context/)