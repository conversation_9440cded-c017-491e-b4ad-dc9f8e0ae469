## Architecture: data / services / routes

This app organizes backend logic into three layers to keep concerns clear and testable.

### data/

- Purpose: persistence-only helpers per table.
- Rules:
  - Accept a typed Supabase client (`SupabaseClient<Database>`) and the `userId` when needed.
  - No cross-table orchestration, side effects, or external API calls.
  - Return raw data structures or throw typed errors.
- Example: `src/data/tasks.ts` with `createTask`, `listTasksForDate`.

### services/

- Purpose: domain workflows that compose multiple `data/*` functions, add validation, and call external APIs.
- Rules:
  - Accept a typed Supabase client and `userId`.
  - Perform input validation (e.g., zod), orchestration, retries, and logging.
  - Keep I/O boundaries explicit and side effects contained here.
- Example: `src/services/tasks.ts` exporting `createTaskService(db, userId, input)` that validates input and calls `data/tasks`.

### routes (App Router API)

- Purpose: thin HTTP adapters.
- Rules:
  - Authenticate via `createClient()` (SSR Supabase), parse inputs, and call `services/*` (or `data/*` for simple CRUD).
  - Map results to `NextResponse.json(...)`. Avoid business logic here.
- Example: `src/app/api/tasks/route.ts` calls `listTasksForDate`/`createTask`.

### When to use services/

- Use `services/` when logic spans tables, requires validation, external integrations, or non-trivial orchestration.
- For simple CRUD, call `data/` directly from routes to reduce boilerplate.

### Testing

- `data/*`: unit-like tests against a test database or mocked client.
- `services/*`: unit tests with mocked `data/*` and integration tests for critical flows.
- `routes/*`: integration tests focusing on request/response and auth.

### Conventions

- Inputs/outputs are typed. Avoid `any`. Prefer `unknown` for errors at boundaries and refine with `instanceof Error`.
- Functions are small and verb-named; modules are noun-named by domain (`tasks`, `profiles`, `goals`).
- All DB access flows through the SSR Supabase client to keep RLS enforced per user.

### Error handling & logging

- Data layer (`src/data/*`): perform I/O only. On failure, wrap and throw a typed repository error with the original error as `cause`. Do not translate to HTTP or log beyond optional debug.
- Service layer (`src/services/*`): orchestrate workflows. Catch low-level errors and translate them into domain errors enriched with context (ids, invariants). Apply retries only for transient failures. Avoid logging except for optional debug.
- API routes (`src/app/api/*`): boundary for HTTP and centralized logging. Catch all errors, map domain errors to HTTP responses, and log once with a child logger and correlation id. Never leak internal details to clients.
- Normalize error shapes and prefer typed errors; attach `cause` and structured metadata for observability. Use transactions for multi-write consistency where possible; otherwise add compensating actions or soft-fail when the use case prefers progress over strict consistency.
