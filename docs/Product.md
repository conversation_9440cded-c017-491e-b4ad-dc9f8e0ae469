Momentum — Product Spec (Updated)

1. Overview

- Title: Momentum
- Elevator pitch: Momentum is an intelligent workspace for deep work and mindful productivity. It unifies planning, focused execution, and reflection into a single daily flow and adds a gentle AI coach that summarizes, nudges, and synthesizes without getting in the way.
- Status: Web-first MVP targeting individual knowledge workers.

2. Problem and Goals

- Problems
  - Fragmented tools (Notion/Todoist/timers) create friction and lost context.
  - Reviews and planning are easy to forget; feedback loops break.
  - No tight linkage between tasks, focus time, and outcomes.
- Product goals
  - One place to plan → execute (with a task-aware timer) → review.
  - Close the loop daily, then roll up to weekly/monthly insights.
  - Keep users in flow: minimal clicks, keyboard-first, non-blocking AI.
- Non-goals (MVP)
  - Team collaboration, calendar write, two-way sync, mobile/desktop apps.

3. Core Philosophy

- Structure with intelligence: predictable UI augmented by light AI.
- Close the loop: Planning → Execution → Reflection feeds the next day.
- Guided, not prescriptive: user decides; AI suggests and automates, never blocks.
- Reliability > cleverness: timer and reminders must be rock-solid.

4. Primary User Journey (Daily Compass)

- Layout: Left (North Star goals), Center (Today’s Path tasks), Right (Log).
- Morning Kick-off (first open after user’s morning_time)
  - Show: unfinished from yesterday + “Ideas for tomorrow” from last review.
  - Choose top 1–3 to plan; Snooze or Backlog the rest.
  - Optional Todoist import flow (choose project/label; one-way; dedupe).
- Focus Sessions
  - Start/pause/complete tied to a task with a reliable timer.
  - Focus Mode dims other panes; post-session mini modal for quick notes.
  - Auto-log session start/end; persist across reloads; one active session per user.
- Evening Review
  - Reminder at evening_time; template: What went well / Could be better / Ideas for tomorrow.
  - Header counts: tasks completed, focus minutes, sessions, progress by goal.
  - Async AI summary generates after submission; never blocks reviewing.
- Next Morning Carry-forward
  - Intentional carry-forward prompt. Options: Carry to today, Snooze to date, Backlog.
  - If task rolls over repeatedly, suggest “Split into next step.”

5. Key Product Decisions

- Tasks are scoped to a day via planned_for_date. Unfinished tasks are carried forward intentionally, not auto-completed. History (focus sessions, notes) stays on the same task ID.
- Track “time to finish” as:
  - Cycle time: completed_at − first_planned_date (or created_at).
  - Active work time: sum of focus_sessions.duration_sec.
- AI usage (MVP): non-blocking daily summaries and optional “Ask Momentum” insights panel.
- Analytics: Use Amplitude via a server proxy. No internal generic events table; compute user-facing metrics from first-party tables.

6. MVP Scope (feature list)

- Daily Compass UI with Goals | Today | Log
- Tasks: CRUD, reorder, complete, keyboard shortcuts (T add task, J quick note, / search, Cmd/Ctrl+Enter complete)
- Carry-forward: first-open detector after morning_time; prompt with Carry/Snooze/Backlog
- Focus Sessions: start/pause/complete, heartbeat, local restore, single active guard, auto log, completion mini modal
- Evening Review: template + counts header; store “Ideas for tomorrow”
- Notifications: web push (morning/evening + session end) with email fallback
- AI Summary: async, minimal; “Generating…” state; respects AI settings
- Todoist Import (read-only): OAuth, choose project/label, one-way import with backlink and dedupe
- Settings: morning/evening times, timer default, notifications on/off, AI on/off
- Performance/reliability: timer >95% across reloads/idle; P95 load <1.5s; Sentry clean

7. “Ask Momentum” (post-MVP, fast follow)

- One free-form box with date range and suggestion chips: Weekly review, Monthly summary, Brag doc (6 months), LinkedIn responsibilities, Time by goal, Top wins/blockers.
- Intent routing (rules-based) to structured generators; otherwise generic Q&A using selected range.
- Save outputs as documents; list under Insights > Documents.
- Tables: insights_requests (job), insight_reports (output), ai_documents (saved docs).
- Privacy controls: settings.allow_external_ai; goals.exclude_from_ai; log_entries.sensitive; optional redaction.

8. Success Metrics (MVP KPIs)

- Planning compliance: percent of weekdays the user selects today’s tasks.
- Review compliance: percent of weekdays an evening review is submitted.
- Focus reliability: sessions completed without error; timer resume success after reload.
- Flow adherence: tasks planned vs completed per day; carry-over rate.
- Delight indicators (qualitative): “This reduced my app switching,” “I’m planning/reviewing more consistently.”

9. Architecture and Stack

- Frontend: Next.js (App Router), TypeScript (strict), Tailwind + shadcn/ui, Sentry, Service Worker (push)
- Backend: Supabase (Postgres, Auth, RLS), Supabase JS SSR client
- Worker: Railway worker for cron (morning/evening reminders, AI jobs, Todoist re-imports)
- Integrations: Todoist OAuth (read), Email provider (Resend/Postmark), LLM provider (OpenAI or Anthropic)
- Analytics: Amplitude via /api/analytics proxy (server-enriched, privacy-sanitized)

10. Data Model (high-level)

- profiles(user_id, tz)
- settings(user_id, morning_time, evening_time, timer_default_min, notifications_on, ai_on, allow_external_ai)
- goals(user_id, title, order, status, exclude_from_ai)
- tasks(user_id, goal_id?, title, status, planned_for_date, first_planned_date, started_at, completed_at, rollover_count, order, external_source, external_id, link)
- focus_sessions(user_id, task_id, start_at, end_at, duration_sec, interruptions, note) with unique active per user
- log_entries(user_id, timestamp, kind(session_start|session_end|note|task_done|task_carried), text, task_id?, sensitive)
- day_reviews(user_id, date, summary, did_well[], could_better[], ideas_for_tomorrow[], ai_summary)
- integration_accounts(user_id, provider, tokens, scopes, expires_at)
- push_subscriptions(user_id, endpoint, keys)
- insights_requests(user_id, type, period_start/end, params, cache_key, facts_hash?, status, provider/model/cost/tokens, attempts, timestamps)
- insight_reports(insights_request_id, user_id, version, title, content_md, content_json?, source_refs[])
- ai_documents(user_id, kind, range_start/end, title, content_md, source_refs[], provider/model/tokens)

11. Core Flows (detailed)

- Morning Kick-off
  - Eligibility: current_time ≥ morning_time (user tz), “today” not planned or snoozed.
  - Screen: Yesterday’s unfinished, Ideas for tomorrow, optional Todoist import selection.
  - Actions: Carry all; Pick top 1–3; Snooze; Backlog; Start day.
- Focus Session
  - Start: insert focus_session, log session_start; persist active session ID in localStorage; show Focus Mode.
  - Heartbeat: every ~15–20s; if page hidden and heartbeat missed >60s, soft pause and confirm on return.
  - Complete: end_at, duration_sec, log session_end; show mini modal for a quick note; attach note to session and log.
- Evening Review
  - Reminder: push/email at evening_time.
  - Review UI: counts header; template inputs; submit writes day_reviews; enqueue AI summary job.
  - AI: background job reads the day’s facts and writes ai_summary; UI shows “Generating…” until ready.
- Carry-forward
  - On first open next day: prompt to carry; updating planned_for_date and rollover_count, log task_carried.
  - Suggest split if rollover_count ≥ 3.

12. Notifications

- Web push via VAPID and Service Worker; subscribe and store endpoints.
- Morning/evening cron in worker (based on profiles.tz + settings times); email fallback if push not permitted.
- Session end: if timer completes in background, show notification; on return, show completion modal.

13. Integrations (MVP: Todoist import only)

- OAuth (read scope). User selects project/label for import during Kick-off or from Settings.
- One-way import with dedupe by external_id; maintain backlink URL. Never overwrite local edits to title.
- Re-import action is idempotent.

14. AI: Summaries and “Ask Momentum”

- Daily summaries (MVP)
  - Input: completed tasks (title/date/goal), focus totals by goal, review notes.
  - Behavior: async generation; never blocks review; rate-limited; clear “Generating…” state.
- “Ask Momentum” (post-MVP)
  - UI: free-form box + date range + suggestions; “Save as document.”
  - Intent router: weekly_review, brag_doc, responsibilities, generic.
  - Data: strictly structured facts; respect AI privacy flags; optional redaction.
  - Caching: insights_requests.cache_key + optional facts_hash to skip regenerating unchanged outputs.
  - Cost control: token tracking; map-reduce for long ranges (e.g., 6-month brag doc).

15. Privacy and Trust

- RLS everywhere; all data scoped to user_id at the DB layer.
- AI controls:
  - settings.ai_on + settings.allow_external_ai
  - goals.exclude_from_ai; log_entries.sensitive
  - Redaction pass for shareable outputs (strip emails/URLs/IDs)
- Data export (JSON) for transparency (Settings).

16. Analytics Plan

- Amplitude (via /api/analytics proxy) for product analytics (funnels, retention, notifications CTR).
- Do not send free-text or titles; send IDs/hashes and counts only.
- Server-derived operational metrics from first-party tables (planning/review compliance, timer reliability, streaks).

17. Acceptance Criteria (MVP)

- Plan → Focus → Review works for 5 consecutive weekdays without integrations.
- Timer reliability ≥ 95% across reloads, idle, and tab switching; single active session enforced.
- Morning Kick-off appears once/day (unless snoozed); carry-forward updates planned_for_date and logs task_carried.
- Evening reminders fire on time in user’s timezone; review submits even if AI summary pending; summary appears within a reasonable window (<60s typical, with retry/backoff).
- Todoist import dedupes by external_id and preserves backlinks; re-import is idempotent.
- Performance: P95 page load < 1.5s in staging; no recurring Sentry errors.

18. Roadmap

- MVP (Weeks 1–3)
  - Foundations (Next.js, Supabase, RLS, CI/CD, Sentry), Daily Compass, Timer, Kick-off, Review, Notifications, Todoist import, AI daily summary.
- Fast follow (Week 4)
  - “Ask Momentum” insights panel + insights tables; documents page; caching + privacy gates.
- Beta polish
  - Streaks; Weekly Review page; gentle over-planning guardrails; drag Goals → Today (non-AI subtasks).
  - Timer enhancements: auto-pause on system lock/idle.
  - PWA + offline (today’s cache, background sync).
  - Integrations: Notion one-time goal import; Calendar read-only.
- Later (v1+)
  - Desktop helper (Tauri/Electron); iOS/Android (Capacitor/RN) with offline timer.
  - Deeper AI coaching: morning top-3, post-session synthesis, evening prompts.
  - Two-way Todoist sync; calendar write (block focus time); Slack/email digests.
  - Privacy: local encryption for sensitive notes; retention controls.

19. Risks and Mitigations

- Timer drift/background throttling: server heartbeat + visibility handling; confirm durations on completion; single-active guard in DB.
- Notification deliverability: early prompt for push; email fallback; in-app banner when missed.
- Timezone/DST complexity: store UTC timestamps; derive “day” using user tz + morning_time boundary.
- Multi-tab races: block starting a session if one active; optional BroadcastChannel leader election.
- OAuth token expiry: refresh tokens stored; preemptive refresh; backoff on 401.

20. Open Questions (current answers)

- Embeddings/semantic search: defer; use structured facts for insights. Revisit later if needed.
- Saved outputs: documents page with filters (kind/range); link from Weekly/Monthly views.
- Internal events table: no; use Amplitude + compute metrics from first-party tables.
- Provider defaults: OpenAI GPT-4o-mini or Anthropic Sonnet; provider switch + strict rate limits.
- Notion import: defer to Beta; create “North Star” goals manually for MVP.

If you want, I can turn this into:

- A Notion/GitHub checklist with milestones and acceptance criteria,
- A one-page UX flow map (screens/states) for Kick-off, Timer, Review, and Ask Momentum,
- A technical implementation plan mapping endpoints/server actions to each feature.
