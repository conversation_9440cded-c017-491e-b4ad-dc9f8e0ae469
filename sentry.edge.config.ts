import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN ?? process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Enable Sentry in prod by default; allow opt-in in development
  enabled:
    process.env.NODE_ENV !== 'development' ||
    process.env.SENTRY_ENABLE_DEV === 'true',

  // Environment and release tagging
  environment: process.env.VERCEL_ENV ?? process.env.NODE_ENV ?? 'development',
  release: process.env.SENTRY_RELEASE ?? process.env.VERCEL_GIT_COMMIT_SHA,

  // Performance monitoring for edge runtime
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

  // Edge runtime configuration
  beforeSend(event) {
    if (
      process.env.NODE_ENV === 'development' &&
      process.env.SENTRY_ENABLE_DEV !== 'true'
    ) {
      return null;
    }
    return event;
  },
});
